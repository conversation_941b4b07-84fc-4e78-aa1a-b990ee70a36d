<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理系统首页</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        /* 主容器 */
        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏 */
        .sidebar {
            width: 240px;
            background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 2px 0 8px rgba(0,0,0,0.1);
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar-header h2 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .sidebar-header .subtitle {
            font-size: 12px;
            opacity: 0.8;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-left-color: #60a5fa;
        }

        .nav-link.active {
            background-color: rgba(255,255,255,0.15);
            color: white;
            border-left-color: #3b82f6;
        }

        .nav-link i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            margin-left: 240px;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航 */
        .top-navbar {
            background: white;
            padding: 0 30px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border-bottom: 1px solid #e5e7eb;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            color: #6b7280;
            font-size: 14px;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .logout-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .logout-btn:hover {
            background: #dc2626;
        }

        /* 页面内容 */
        .page-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        /* 欢迎区域 */
        .welcome-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 16px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: translate(50%, -50%);
        }

        .welcome-content {
            position: relative;
            z-index: 1;
        }

        .welcome-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .welcome-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .welcome-stats {
            display: flex;
            gap: 30px;
            margin-top: 20px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .stat-icon {
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.2);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-left: 4px solid #3b82f6;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-card.users {
            border-left-color: #10b981;
        }

        .stat-card.admins {
            border-left-color: #f59e0b;
        }

        .stat-card.active {
            border-left-color: #8b5cf6;
        }

        .stat-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 16px;
        }

        .stat-title {
            font-size: 14px;
            color: #6b7280;
            font-weight: 500;
        }

        .stat-icon-large {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .stat-icon-large.users {
            background: #dcfce7;
            color: #166534;
        }

        .stat-icon-large.admins {
            background: #fef3c7;
            color: #92400e;
        }

        .stat-icon-large.active {
            background: #f3e8ff;
            color: #7c3aed;
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .stat-change {
            font-size: 12px;
            color: #10b981;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* 快捷操作 */
        .quick-actions {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .action-card {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            text-decoration: none;
            color: #374151;
            transition: all 0.3s ease;
        }

        .action-card:hover {
            border-color: #3b82f6;
            background: #f8fafc;
            transform: translateY(-1px);
        }

        .action-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .action-icon.primary {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .action-icon.success {
            background: #dcfce7;
            color: #166534;
        }

        .action-icon.warning {
            background: #fef3c7;
            color: #92400e;
        }

        .action-content {
            flex: 1;
        }

        .action-title {
            font-weight: 500;
            margin-bottom: 2px;
        }

        .action-desc {
            font-size: 12px;
            color: #6b7280;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .main-content {
                margin-left: 0;
            }

            .top-navbar {
                padding: 0 15px;
            }

            .page-content {
                padding: 20px 15px;
            }

            .welcome-section {
                padding: 24px;
            }

            .welcome-title {
                font-size: 24px;
            }

            .welcome-stats {
                flex-direction: column;
                gap: 16px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2>管理系统</h2>
                <div class="subtitle">Java Web Application</div>
            </div>
            
            <div class="sidebar-nav">
                <div class="nav-item">
                    <a href="/" class="nav-link active">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="/users/modern" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="/test-merged-user" class="nav-link">
                        <i class="fas fa-flask"></i>
                        <span>功能测试</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航 -->
            <header class="top-navbar">
                <div class="breadcrumb">
                    <span>首页</span>
                </div>
                
                <div class="user-menu">
                    <div class="user-info">
                        <div class="user-avatar" th:text="${#strings.substring(#authentication.name, 0, 1).toUpperCase()}">A</div>
                        <span sec:authentication="name">用户</span>
                        <span class="text-muted">|</span>
                        <span sec:authentication="principal.authorities" style="font-size: 12px; color: #6b7280;">角色</span>
                    </div>
                    
                    <form th:action="@{/logout}" method="post" style="margin: 0;">
                        <button type="submit" class="logout-btn">
                            <i class="fas fa-sign-out-alt"></i> 退出
                        </button>
                    </form>
                </div>
            </header>

            <!-- 页面内容 -->
            <main class="page-content">
                <!-- 欢迎区域 -->
                <div class="welcome-section">
                    <div class="welcome-content">
                        <h1 class="welcome-title">欢迎回来！</h1>
                        <p class="welcome-subtitle">
                            您好，<span sec:authentication="name">用户</span>！今天是 <span th:text="${#temporals.format(#temporals.createNow(), 'yyyy年MM月dd日')}">日期</span>
                        </p>
                        <div class="welcome-stats">
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <span>最后登录：刚刚</span>
                            </div>
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <span>角色：<span sec:authentication="principal.authorities">角色</span></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card users">
                        <div class="stat-header">
                            <div>
                                <div class="stat-title">总用户数</div>
                                <div class="stat-value" id="totalUsers">-</div>
                                <div class="stat-change">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>较上周 +12%</span>
                                </div>
                            </div>
                            <div class="stat-icon-large users">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card admins">
                        <div class="stat-header">
                            <div>
                                <div class="stat-title">管理员数</div>
                                <div class="stat-value" id="adminUsers">-</div>
                                <div class="stat-change">
                                    <i class="fas fa-minus"></i>
                                    <span>无变化</span>
                                </div>
                            </div>
                            <div class="stat-icon-large admins">
                                <i class="fas fa-user-shield"></i>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card active">
                        <div class="stat-header">
                            <div>
                                <div class="stat-title">活跃用户</div>
                                <div class="stat-value" id="activeUsers">-</div>
                                <div class="stat-change">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>较昨日 +5%</span>
                                </div>
                            </div>
                            <div class="stat-icon-large active">
                                <i class="fas fa-chart-line"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="quick-actions">
                    <h2 class="section-title">快捷操作</h2>
                    <div class="actions-grid">
                        <a href="/users/modern" class="action-card">
                            <div class="action-icon primary">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">用户管理</div>
                                <div class="action-desc">查看和管理系统用户</div>
                            </div>
                        </a>

                        <a href="/users/new" class="action-card">
                            <div class="action-icon success">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">新增用户</div>
                                <div class="action-desc">创建新的系统用户</div>
                            </div>
                        </a>

                        <a href="/test-merged-user" class="action-card">
                            <div class="action-icon warning">
                                <i class="fas fa-flask"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">功能测试</div>
                                <div class="action-desc">测试系统各项功能</div>
                            </div>
                        </a>

                        <a href="/test-update-user" class="action-card">
                            <div class="action-icon primary">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">更新测试</div>
                                <div class="action-desc">测试用户更新功能</div>
                            </div>
                        </a>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // 加载统计数据
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
        });

        function loadStatistics() {
            // 获取用户统计数据
            fetch('/test/count')
                .then(response => response.text())
                .then(data => {
                    // 解析返回的文本，提取数字
                    const match = data.match(/(\d+)/);
                    if (match) {
                        const totalCount = parseInt(match[1]);
                        document.getElementById('totalUsers').textContent = totalCount;
                        
                        // 模拟其他统计数据
                        document.getElementById('adminUsers').textContent = Math.max(1, Math.floor(totalCount * 0.1));
                        document.getElementById('activeUsers').textContent = Math.max(1, Math.floor(totalCount * 0.8));
                    }
                })
                .catch(error => {
                    console.error('加载统计数据失败:', error);
                    // 设置默认值
                    document.getElementById('totalUsers').textContent = '0';
                    document.getElementById('adminUsers').textContent = '0';
                    document.getElementById('activeUsers').textContent = '0';
                });
        }
    </script>
</body>
</html>
