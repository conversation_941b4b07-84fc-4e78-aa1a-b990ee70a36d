<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>
    <title th:text="${pageTitle} ?: '管理系统'">管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        /* 主容器 */
        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏 */
        .sidebar {
            width: 240px;
            background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 2px 0 8px rgba(0,0,0,0.1);
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar-header h2 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .sidebar-header .subtitle {
            font-size: 12px;
            opacity: 0.8;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-left-color: #60a5fa;
        }

        .nav-link.active {
            background-color: rgba(255,255,255,0.15);
            color: white;
            border-left-color: #3b82f6;
        }

        .nav-link i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            margin-left: 240px;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航 */
        .top-navbar {
            background: white;
            padding: 0 30px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border-bottom: 1px solid #e5e7eb;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            color: #6b7280;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #3b82f6;
            text-decoration: none;
        }

        .breadcrumb .separator {
            margin: 0 8px;
            color: #d1d5db;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .logout-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .logout-btn:hover {
            background: #dc2626;
        }

        /* 页面内容 */
        .page-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .page-description {
            color: #6b7280;
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .top-navbar {
                padding: 0 15px;
            }

            .page-content {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2>管理系统</h2>
                <div class="subtitle">Java Web Application</div>
            </div>
            
            <div class="sidebar-nav">
                <div class="nav-item">
                    <a href="/" class="nav-link" th:classappend="${#httpServletRequest.requestURI == '/'} ? 'active' : ''">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="/users" class="nav-link" th:classappend="${#strings.startsWith(#httpServletRequest.requestURI, '/users')} ? 'active' : ''">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="/test-merged-user" class="nav-link" th:classappend="${#strings.startsWith(#httpServletRequest.requestURI, '/test-')} ? 'active' : ''">
                        <i class="fas fa-flask"></i>
                        <span>功能测试</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航 -->
            <header class="top-navbar">
                <div class="breadcrumb">
                    <a href="/">首页</a>
                    <span class="separator">/</span>
                    <span th:text="${pageTitle} ?: '页面'">页面</span>
                </div>
                
                <div class="user-menu">
                    <div class="user-info">
                        <div class="user-avatar" th:text="${#strings.substring(#authentication.name, 0, 1).toUpperCase()}">A</div>
                        <span sec:authentication="name">用户</span>
                        <span class="text-muted">|</span>
                        <span sec:authentication="principal.authorities" style="font-size: 12px; color: #6b7280;">角色</span>
                    </div>
                    
                    <form th:action="@{/logout}" method="post" style="margin: 0;">
                        <button type="submit" class="logout-btn">
                            <i class="fas fa-sign-out-alt"></i> 退出
                        </button>
                    </form>
                </div>
            </header>

            <!-- 页面内容 -->
            <main class="page-content">
                <div class="page-header" th:if="${pageTitle}">
                    <h1 class="page-title" th:text="${pageTitle}">页面标题</h1>
                    <p class="page-description" th:if="${pageDescription}" th:text="${pageDescription}">页面描述</p>
                </div>
                
                <!-- 页面具体内容插槽 -->
                <div th:replace="${contentTemplate}">
                    页面内容
                </div>
            </main>
        </div>
    </div>

    <script>
        // 移动端菜单切换
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('mobile-open');
        }

        // 自动高亮当前页面菜单
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');
            
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
