<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>
    <title th:text="${isEdit ? '编辑角色' : '新增角色'}">角色表单</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .form-floating .form-control {
            border-radius: 10px;
        }
        .btn-save {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 10px;
        }
        .btn-save:hover {
            background: linear-gradient(135deg, #218838 0%, #1aa085 100%);
        }
        .permission-group {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .permission-group h6 {
            margin-bottom: 10px;
            color: #495057;
            font-weight: 600;
        }
        .form-check {
            margin-bottom: 8px;
        }
        .permission-description {
            font-size: 0.875rem;
            color: #6c757d;
            margin-left: 24px;
            margin-top: -5px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-code-square"></i>
                Java Web应用
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="bi bi-house"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/users">
                            <i class="bi bi-people"></i> 用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/roles">
                            <i class="bi bi-shield-check"></i> 角色管理
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-flask"></i> 测试功能
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/test-merged-user">合并功能测试</a></li>
                            <li><a class="dropdown-item" href="/test-update-user">更新功能测试</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/hello">问候页面</a></li>
                        </ul>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            <span sec:authentication="name">用户</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">用户信息</h6></li>
                            <li><span class="dropdown-item-text">角色: <span sec:authentication="principal.authorities">角色</span></span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form th:action="@{/logout}" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="bi bi-box-arrow-right"></i> 退出登录
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">
                            <i class="bi" th:classappend="${isEdit ? 'bi-pencil-square' : 'bi-plus-circle'}"></i>
                            <span th:text="${isEdit ? '编辑角色' : '新增角色'}">角色表单</span>
                        </h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="/">首页</a></li>
                                <li class="breadcrumb-item"><a href="/roles">角色管理</a></li>
                                <li class="breadcrumb-item active" th:text="${isEdit ? '编辑角色' : '新增角色'}">角色表单</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="/roles" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 角色表单 -->
        <div class="row">
            <div class="col-lg-8">
                <form th:action="${isEdit ? '/roles/' + role.id : '/roles'}" 
                      th:method="${isEdit ? 'PUT' : 'POST'}" 
                      th:object="${role}" 
                      class="needs-validation" 
                      novalidate>
                    
                    <!-- 基本信息 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-info-circle"></i> 基本信息
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <!-- 角色名称 -->
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" 
                                               class="form-control" 
                                               id="name" 
                                               th:field="*{name}" 
                                               placeholder="角色名称" 
                                               required
                                               pattern="[A-Z_]+"
                                               title="角色名称只能包含大写字母和下划线">
                                        <label for="name">
                                            <i class="bi bi-tag me-2"></i>角色名称 *
                                        </label>
                                        <div class="invalid-feedback">
                                            请输入有效的角色名称（大写字母和下划线）
                                        </div>
                                        <div class="form-text">例如：ADMIN, USER, EDITOR</div>
                                    </div>
                                </div>

                                <!-- 显示名称 -->
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" 
                                               class="form-control" 
                                               id="displayName" 
                                               th:field="*{displayName}" 
                                               placeholder="显示名称">
                                        <label for="displayName">
                                            <i class="bi bi-eye me-2"></i>显示名称
                                        </label>
                                        <div class="form-text">用于界面显示的友好名称</div>
                                    </div>
                                </div>

                                <!-- 角色描述 -->
                                <div class="col-12">
                                    <div class="form-floating">
                                        <textarea class="form-control" 
                                                  id="description" 
                                                  th:field="*{description}" 
                                                  placeholder="角色描述"
                                                  style="height: 100px"></textarea>
                                        <label for="description">
                                            <i class="bi bi-file-text me-2"></i>角色描述
                                        </label>
                                    </div>
                                </div>

                                <!-- 启用状态 -->
                                <div class="col-12">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="enabled" 
                                               th:field="*{enabled}"
                                               th:checked="${role.enabled != null ? role.enabled : true}">
                                        <label class="form-check-label" for="enabled">
                                            <i class="bi bi-toggle-on me-2"></i>启用角色
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 权限配置 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-key"></i> 权限配置
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- 全选/全不选 -->
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                    <label class="form-check-label fw-bold" for="selectAll">
                                        全选/全不选
                                    </label>
                                </div>
                                <hr>
                            </div>

                            <!-- 用户管理权限 -->
                            <div class="permission-group">
                                <h6><i class="bi bi-people me-2"></i>用户管理权限</h6>
                                <div th:each="permission : ${allPermissions}" th:if="${#strings.startsWith(permission.name(), 'USER_')}">
                                    <div class="form-check">
                                        <input class="form-check-input permission-checkbox" 
                                               type="checkbox" 
                                               th:id="'perm_' + ${permission.name()}"
                                               th:value="${permission.name()}"
                                               name="permissions"
                                               th:checked="${role.permissions != null && role.permissions.contains(permission)}">
                                        <label class="form-check-label" th:for="'perm_' + ${permission.name()}" th:text="${permission.displayName}">
                                            权限名称
                                        </label>
                                    </div>
                                    <div class="permission-description" th:text="${permission.description}">权限描述</div>
                                </div>
                            </div>

                            <!-- 角色管理权限 -->
                            <div class="permission-group">
                                <h6><i class="bi bi-shield-check me-2"></i>角色管理权限</h6>
                                <div th:each="permission : ${allPermissions}" th:if="${#strings.startsWith(permission.name(), 'ROLE_')}">
                                    <div class="form-check">
                                        <input class="form-check-input permission-checkbox" 
                                               type="checkbox" 
                                               th:id="'perm_' + ${permission.name()}"
                                               th:value="${permission.name()}"
                                               name="permissions"
                                               th:checked="${role.permissions != null && role.permissions.contains(permission)}">
                                        <label class="form-check-label" th:for="'perm_' + ${permission.name()}" th:text="${permission.displayName}">
                                            权限名称
                                        </label>
                                    </div>
                                    <div class="permission-description" th:text="${permission.description}">权限描述</div>
                                </div>
                            </div>

                            <!-- 系统管理权限 -->
                            <div class="permission-group">
                                <h6><i class="bi bi-gear me-2"></i>系统管理权限</h6>
                                <div th:each="permission : ${allPermissions}" th:if="${#strings.startsWith(permission.name(), 'SYSTEM_')}">
                                    <div class="form-check">
                                        <input class="form-check-input permission-checkbox" 
                                               type="checkbox" 
                                               th:id="'perm_' + ${permission.name()}"
                                               th:value="${permission.name()}"
                                               name="permissions"
                                               th:checked="${role.permissions != null && role.permissions.contains(permission)}">
                                        <label class="form-check-label" th:for="'perm_' + ${permission.name()}" th:text="${permission.displayName}">
                                            权限名称
                                        </label>
                                    </div>
                                    <div class="permission-description" th:text="${permission.description}">权限描述</div>
                                </div>
                            </div>

                            <!-- 数据权限 -->
                            <div class="permission-group">
                                <h6><i class="bi bi-database me-2"></i>数据权限</h6>
                                <div th:each="permission : ${allPermissions}" th:if="${#strings.startsWith(permission.name(), 'DATA_')}">
                                    <div class="form-check">
                                        <input class="form-check-input permission-checkbox" 
                                               type="checkbox" 
                                               th:id="'perm_' + ${permission.name()}"
                                               th:value="${permission.name()}"
                                               name="permissions"
                                               th:checked="${role.permissions != null && role.permissions.contains(permission)}">
                                        <label class="form-check-label" th:for="'perm_' + ${permission.name()}" th:text="${permission.displayName}">
                                            权限名称
                                        </label>
                                    </div>
                                    <div class="permission-description" th:text="${permission.description}">权限描述</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex gap-3">
                                <button type="submit" class="btn btn-primary btn-save btn-lg">
                                    <i class="bi bi-check-lg me-2"></i>
                                    <span th:text="${isEdit ? '更新角色' : '创建角色'}">保存</span>
                                </button>
                                <a href="/roles" class="btn btn-outline-secondary btn-lg">
                                    <i class="bi bi-x-lg me-2"></i>取消
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 侧边栏帮助 -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-info-circle me-2"></i>填写说明
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><i class="bi bi-check text-success me-2"></i>角色名称为必填项，只能包含大写字母和下划线</li>
                            <li><i class="bi bi-check text-success me-2"></i>显示名称用于界面友好显示</li>
                            <li><i class="bi bi-check text-success me-2"></i>角色描述帮助理解角色用途</li>
                            <li><i class="bi bi-check text-success me-2"></i>权限配置决定角色能执行的操作</li>
                            <li><i class="bi bi-check text-success me-2"></i>可以使用全选快速选择所有权限</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Bootstrap 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                const forms = document.getElementsByClassName('needs-validation');
                const validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // 全选/全不选功能
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.permission-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // 监听权限复选框变化，更新全选状态
        document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const allCheckboxes = document.querySelectorAll('.permission-checkbox');
                const checkedCheckboxes = document.querySelectorAll('.permission-checkbox:checked');
                const selectAllCheckbox = document.getElementById('selectAll');
                
                if (checkedCheckboxes.length === allCheckboxes.length) {
                    selectAllCheckbox.checked = true;
                    selectAllCheckbox.indeterminate = false;
                } else if (checkedCheckboxes.length === 0) {
                    selectAllCheckbox.checked = false;
                    selectAllCheckbox.indeterminate = false;
                } else {
                    selectAllCheckbox.checked = false;
                    selectAllCheckbox.indeterminate = true;
                }
            });
        });

        // 页面加载时检查全选状态
        document.addEventListener('DOMContentLoaded', function() {
            const allCheckboxes = document.querySelectorAll('.permission-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.permission-checkbox:checked');
            const selectAllCheckbox = document.getElementById('selectAll');
            
            if (checkedCheckboxes.length === allCheckboxes.length && allCheckboxes.length > 0) {
                selectAllCheckbox.checked = true;
            } else if (checkedCheckboxes.length > 0) {
                selectAllCheckbox.indeterminate = true;
            }
        });

        // 角色名称自动转换为大写
        document.getElementById('name').addEventListener('input', function() {
            this.value = this.value.toUpperCase().replace(/[^A-Z_]/g, '');
        });

        // 自动填充显示名称
        document.getElementById('name').addEventListener('blur', function() {
            const displayNameField = document.getElementById('displayName');
            if (!displayNameField.value && this.value) {
                // 简单的名称转换逻辑
                const displayName = this.value.toLowerCase()
                    .replace(/_/g, ' ')
                    .replace(/\b\w/g, l => l.toUpperCase());
                displayNameField.value = displayName;
            }
        });
    </script>
</body>
</html>
