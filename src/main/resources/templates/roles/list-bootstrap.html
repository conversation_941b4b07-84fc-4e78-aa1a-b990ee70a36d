<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>
    <title>角色管理</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .role-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #28a745, #20c997);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        .table th {
            border-top: none;
            font-weight: 600;
            color: #495057;
            background-color: #f8f9fa;
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .permission-badge {
            font-size: 0.7rem;
            margin: 1px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-code-square"></i>
                Java Web应用
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="bi bi-house"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/users">
                            <i class="bi bi-people"></i> 用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/roles">
                            <i class="bi bi-shield-check"></i> 角色管理
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-flask"></i> 测试功能
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/test-merged-user">合并功能测试</a></li>
                            <li><a class="dropdown-item" href="/test-update-user">更新功能测试</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/hello">问候页面</a></li>
                        </ul>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            <span sec:authentication="name">用户</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">用户信息</h6></li>
                            <li><span class="dropdown-item-text">角色: <span sec:authentication="principal.authorities">角色</span></span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form th:action="@{/logout}" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="bi bi-box-arrow-right"></i> 退出登录
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 页面标题和操作 -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">
                            <i class="bi bi-shield-check"></i> 角色管理
                        </h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="/">首页</a></li>
                                <li class="breadcrumb-item active">角色管理</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="/roles/new" class="btn btn-primary" sec:authorize="hasAuthority('ROLE_CREATE') or hasRole('ADMIN')">
                            <i class="bi bi-plus-circle"></i> 新增角色
                        </a>
                        <button class="btn btn-outline-secondary" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="card mb-4">
            <div class="card-body">
                <form class="row g-3" id="searchForm">
                    <div class="col-md-6">
                        <label for="searchInput" class="form-label">搜索角色</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control" id="searchInput" placeholder="输入角色名称或显示名称">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="statusFilter" class="form-label">状态筛选</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="true">启用</option>
                            <option value="false">禁用</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="applyFilters()">
                                <i class="bi bi-funnel"></i> 筛选
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 角色列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i> 角色列表
                </h5>
                <span class="badge bg-primary">
                    共 <span th:text="${roles.size()}">0</span> 个角色
                </span>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">角色信息</th>
                                <th scope="col">角色名称</th>
                                <th scope="col">权限</th>
                                <th scope="col">状态</th>
                                <th scope="col">创建时间</th>
                                <th scope="col">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="role, iterStat : ${roles}">
                                <td th:text="${iterStat.count}">1</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="role-icon me-3" th:text="${#strings.substring(role.displayName ?: role.name, 0, 1).toUpperCase()}">R</div>
                                        <div>
                                            <div class="fw-bold" th:text="${role.displayName ?: role.name}">角色显示名</div>
                                            <small class="text-muted" th:text="${role.description ?: '无描述'}">角色描述</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <code th:text="${role.name}">ROLE_NAME</code>
                                </td>
                                <td>
                                    <div class="d-flex flex-wrap">
                                        <span th:each="permission : ${role.permissions}" 
                                              class="badge bg-secondary permission-badge me-1 mb-1" 
                                              th:text="${permission.displayName}"
                                              th:title="${permission.description}">
                                            权限
                                        </span>
                                        <span th:if="${role.permissions.empty}" class="text-muted small">无权限</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge status-badge" th:classappend="${role.enabled} ? 'bg-success' : 'bg-danger'">
                                        <i class="bi" th:classappend="${role.enabled} ? 'bi-check-circle' : 'bi-x-circle'"></i>
                                        <span th:text="${role.enabled} ? '启用' : '禁用'">状态</span>
                                    </span>
                                </td>
                                <td>
                                    <small th:text="${#temporals.format(role.createdAt, 'yyyy-MM-dd HH:mm')}">创建时间</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a th:href="@{/roles/{id}(id=${role.id})}" class="btn btn-outline-info btn-sm" title="查看详情"
                                           sec:authorize="hasAuthority('ROLE_READ') or hasRole('ADMIN')">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a th:href="@{/roles/{id}/edit(id=${role.id})}" class="btn btn-outline-warning btn-sm" title="编辑"
                                           sec:authorize="hasAuthority('ROLE_UPDATE') or hasRole('ADMIN')">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button class="btn btn-outline-success btn-sm" th:onclick="'toggleRole(' + ${role.id} + ')'" 
                                                title="切换状态" sec:authorize="hasAuthority('ROLE_UPDATE') or hasRole('ADMIN')">
                                            <i class="bi bi-toggle-on"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" th:onclick="'deleteRole(' + ${role.id} + ')'" 
                                                title="删除" sec:authorize="hasAuthority('ROLE_DELETE') or hasRole('ADMIN')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div th:if="${roles.empty}" class="text-center py-5">
            <i class="bi bi-shield-check" style="font-size: 4rem; color: #dee2e6;"></i>
            <h4 class="text-muted mt-3">暂无角色数据</h4>
            <p class="text-muted">点击上方"新增角色"按钮创建第一个角色</p>
            <a href="/roles/new" class="btn btn-primary" sec:authorize="hasAuthority('ROLE_CREATE') or hasRole('ADMIN')">
                <i class="bi bi-plus-circle"></i> 新增角色
            </a>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 删除角色功能
        function deleteRole(roleId) {
            if (confirm('确定要删除这个角色吗？此操作不可撤销。')) {
                const csrfToken = document.querySelector('meta[name="_csrf"]')?.getAttribute('content');
                const csrfHeader = document.querySelector('meta[name="_csrf_header"]')?.getAttribute('content');
                
                const headers = {
                    'Content-Type': 'application/json',
                };
                
                if (csrfToken && csrfHeader) {
                    headers[csrfHeader] = csrfToken;
                }
                
                fetch('/roles/api/' + roleId, {
                    method: 'DELETE',
                    headers: headers
                })
                .then(response => {
                    if (response.ok) {
                        showAlert('角色删除成功！', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert('删除失败，请重试', 'danger');
                    }
                })
                .catch(error => {
                    console.error('删除失败:', error);
                    showAlert('删除失败，请重试', 'danger');
                });
            }
        }

        // 切换角色状态
        function toggleRole(roleId) {
            const csrfToken = document.querySelector('meta[name="_csrf"]')?.getAttribute('content');
            const csrfHeader = document.querySelector('meta[name="_csrf_header"]')?.getAttribute('content');
            
            const headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
            };
            
            if (csrfToken && csrfHeader) {
                headers[csrfHeader] = csrfToken;
            }
            
            fetch('/roles/' + roleId + '/toggle', {
                method: 'POST',
                headers: headers
            })
            .then(response => {
                if (response.ok) {
                    showAlert('角色状态切换成功！', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert('状态切换失败，请重试', 'danger');
                }
            })
            .catch(error => {
                console.error('状态切换失败:', error);
                showAlert('状态切换失败，请重试', 'danger');
            });
        }

        // 显示提示信息
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 3000);
        }

        // 应用筛选
        function applyFilters() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            
            const rows = document.querySelectorAll('tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const roleName = row.querySelector('.fw-bold').textContent.toLowerCase();
                const roleCode = row.querySelector('code').textContent.toLowerCase();
                const roleStatus = row.querySelector('.status-badge').textContent.trim();
                
                let showRow = true;
                
                // 搜索筛选
                if (searchTerm && !roleName.includes(searchTerm) && !roleCode.includes(searchTerm)) {
                    showRow = false;
                }
                
                // 状态筛选
                if (statusFilter && !roleStatus.includes(statusFilter === 'true' ? '启用' : '禁用')) {
                    showRow = false;
                }
                
                row.style.display = showRow ? '' : 'none';
                if (showRow) visibleCount++;
            });
            
            // 更新计数
            document.querySelector('.badge.bg-primary').innerHTML = `共 ${visibleCount} 个角色`;
        }

        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                applyFilters();
            }
        });

        // 实时搜索
        document.getElementById('searchInput').addEventListener('input', applyFilters);
        document.getElementById('statusFilter').addEventListener('change', applyFilters);
    </script>
</body>
</html>
