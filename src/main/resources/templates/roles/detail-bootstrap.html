<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色详情</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .role-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #28a745, #20c997);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 32px;
        }
        .permission-badge {
            font-size: 0.8rem;
            margin: 2px;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-code-square"></i>
                Java Web应用
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="bi bi-house"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/users">
                            <i class="bi bi-people"></i> 用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/roles">
                            <i class="bi bi-shield-check"></i> 角色管理
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-flask"></i> 测试功能
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/test-merged-user">合并功能测试</a></li>
                            <li><a class="dropdown-item" href="/test-update-user">更新功能测试</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/hello">问候页面</a></li>
                        </ul>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            <span sec:authentication="name">用户</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">用户信息</h6></li>
                            <li><span class="dropdown-item-text">角色: <span sec:authentication="principal.authorities">角色</span></span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form th:action="@{/logout}" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="bi bi-box-arrow-right"></i> 退出登录
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">
                            <i class="bi bi-shield-check"></i> 角色详情
                        </h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="/">首页</a></li>
                                <li class="breadcrumb-item"><a href="/roles">角色管理</a></li>
                                <li class="breadcrumb-item active">角色详情</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a th:href="@{/roles/{id}/edit(id=${role.id})}" class="btn btn-warning"
                           sec:authorize="hasAuthority('ROLE_UPDATE') or hasRole('ADMIN')">
                            <i class="bi bi-pencil"></i> 编辑角色
                        </a>
                        <a href="/roles" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 角色基本信息 -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-info-circle"></i> 基本信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center">
                                <div class="role-icon mx-auto mb-3" th:text="${#strings.substring(role.displayName ?: role.name, 0, 1).toUpperCase()}">R</div>
                                <span class="badge" th:classappend="${role.enabled} ? 'bg-success' : 'bg-danger'">
                                    <i class="bi" th:classappend="${role.enabled} ? 'bi-check-circle' : 'bi-x-circle'"></i>
                                    <span th:text="${role.enabled} ? '启用' : '禁用'">状态</span>
                                </span>
                            </div>
                            <div class="col-md-9">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <span class="info-label">角色名称：</span>
                                        <code th:text="${role.name}">ROLE_NAME</code>
                                    </div>
                                    <div class="col-md-6">
                                        <span class="info-label">显示名称：</span>
                                        <span th:text="${role.displayName ?: '未设置'}">显示名称</span>
                                    </div>
                                    <div class="col-12">
                                        <span class="info-label">角色描述：</span>
                                        <p class="mb-0" th:text="${role.description ?: '无描述'}">角色描述</p>
                                    </div>
                                    <div class="col-md-6">
                                        <span class="info-label">创建时间：</span>
                                        <span th:text="${#temporals.format(role.createdAt, 'yyyy-MM-dd HH:mm:ss')}">创建时间</span>
                                    </div>
                                    <div class="col-md-6" th:if="${role.updatedAt}">
                                        <span class="info-label">更新时间：</span>
                                        <span th:text="${#temporals.format(role.updatedAt, 'yyyy-MM-dd HH:mm:ss')}">更新时间</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 权限信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-key"></i> 权限列表
                            <span class="badge bg-primary ms-2" th:text="${role.permissions.size()}">0</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div th:if="${role.permissions.empty}" class="text-center py-4">
                            <i class="bi bi-key" style="font-size: 3rem; color: #dee2e6;"></i>
                            <h6 class="text-muted mt-3">该角色暂无权限</h6>
                            <p class="text-muted">请编辑角色以添加权限</p>
                        </div>
                        <div th:if="${!role.permissions.empty}" class="row">
                            <div class="col-12">
                                <div class="d-flex flex-wrap">
                                    <span th:each="permission : ${role.permissions}" 
                                          class="badge bg-primary permission-badge me-2 mb-2" 
                                          th:text="${permission.displayName}"
                                          th:title="${permission.description}">
                                        权限
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 权限分类展示 -->
                        <div th:if="${!role.permissions.empty}" class="mt-4">
                            <h6>权限分类：</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-muted">用户管理权限</h6>
                                    <div class="mb-3">
                                        <span th:each="permission : ${role.permissions}" 
                                              th:if="${#strings.startsWith(permission.name(), 'USER_')}"
                                              class="badge bg-info permission-badge me-1 mb-1" 
                                              th:text="${permission.displayName}">
                                        </span>
                                        <span th:if="${role.permissions.stream().noneMatch(p -> p.name().startsWith('USER_'))}" 
                                              class="text-muted small">无</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted">角色管理权限</h6>
                                    <div class="mb-3">
                                        <span th:each="permission : ${role.permissions}" 
                                              th:if="${#strings.startsWith(permission.name(), 'ROLE_')}"
                                              class="badge bg-warning text-dark permission-badge me-1 mb-1" 
                                              th:text="${permission.displayName}">
                                        </span>
                                        <span th:if="${role.permissions.stream().noneMatch(p -> p.name().startsWith('ROLE_'))}" 
                                              class="text-muted small">无</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted">系统管理权限</h6>
                                    <div class="mb-3">
                                        <span th:each="permission : ${role.permissions}" 
                                              th:if="${#strings.startsWith(permission.name(), 'SYSTEM_')}"
                                              class="badge bg-danger permission-badge me-1 mb-1" 
                                              th:text="${permission.displayName}">
                                        </span>
                                        <span th:if="${role.permissions.stream().noneMatch(p -> p.name().startsWith('SYSTEM_'))}" 
                                              class="text-muted small">无</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted">数据权限</h6>
                                    <div class="mb-3">
                                        <span th:each="permission : ${role.permissions}" 
                                              th:if="${#strings.startsWith(permission.name(), 'DATA_')}"
                                              class="badge bg-success permission-badge me-1 mb-1" 
                                              th:text="${permission.displayName}">
                                        </span>
                                        <span th:if="${role.permissions.stream().noneMatch(p -> p.name().startsWith('DATA_'))}" 
                                              class="text-muted small">无</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 侧边栏信息 -->
            <div class="col-lg-4">
                <!-- 操作面板 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-gear"></i> 操作
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a th:href="@{/roles/{id}/edit(id=${role.id})}" class="btn btn-warning"
                               sec:authorize="hasAuthority('ROLE_UPDATE') or hasRole('ADMIN')">
                                <i class="bi bi-pencil"></i> 编辑角色
                            </a>
                            <button class="btn btn-outline-success" th:onclick="'toggleRole(' + ${role.id} + ')'"
                                    sec:authorize="hasAuthority('ROLE_UPDATE') or hasRole('ADMIN')">
                                <i class="bi bi-toggle-on"></i> 
                                <span th:text="${role.enabled} ? '禁用角色' : '启用角色'">切换状态</span>
                            </button>
                            <button class="btn btn-outline-danger" th:onclick="'deleteRole(' + ${role.id} + ')'"
                                    sec:authorize="hasAuthority('ROLE_DELETE') or hasRole('ADMIN')">
                                <i class="bi bi-trash"></i> 删除角色
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-bar-chart"></i> 统计信息
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h4 class="text-primary" th:text="${role.permissions.size()}">0</h4>
                                    <small class="text-muted">权限数量</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h4 class="text-success" th:text="${role.users?.size() ?: 0}">0</h4>
                                <small class="text-muted">关联用户</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 删除角色功能
        function deleteRole(roleId) {
            if (confirm('确定要删除这个角色吗？此操作不可撤销，关联的用户将失去此角色。')) {
                window.location.href = '/roles/' + roleId + '/delete';
            }
        }

        // 切换角色状态
        function toggleRole(roleId) {
            if (confirm('确定要切换角色状态吗？')) {
                fetch('/roles/' + roleId + '/toggle', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    }
                })
                .then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('操作失败，请重试');
                    }
                })
                .catch(error => {
                    console.error('操作失败:', error);
                    alert('操作失败，请重试');
                });
            }
        }
    </script>
</body>
</html>
