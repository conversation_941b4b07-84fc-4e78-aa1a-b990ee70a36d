<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试登出功能</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #1976D2;
        }
        .logout-btn {
            background-color: #f44336;
        }
        .logout-btn:hover {
            background-color: #da190b;
        }
        .user-info {
            background-color: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试登出功能</h1>
        
        <div class="user-info">
            <h3>当前登录用户信息</h3>
            <p><strong>用户名:</strong> <span sec:authentication="name">用户</span></p>
            <p><strong>角色:</strong> <span sec:authentication="principal.authorities">角色</span></p>
        </div>
        
        <div>
            <h3>测试登出按钮</h3>
            <p>点击下面的按钮测试登出功能：</p>
            
            <!-- 正确的登出表单 -->
            <form th:action="@{/logout}" method="post" style="display: inline;">
                <button type="submit" class="btn logout-btn">正确的登出 (POST)</button>
            </form>
            
            <!-- 错误的登出链接（用于对比） -->
            <a href="/logout" class="btn" style="background-color: #ff9800;">错误的登出 (GET)</a>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="/" class="btn">返回首页</a>
            <a href="/users" class="btn">用户管理</a>
        </div>
        
        <div style="margin-top: 20px; color: #666; font-size: 14px;">
            <p><strong>说明：</strong></p>
            <p>• "正确的登出 (POST)" 按钮使用POST方法，应该能正常登出</p>
            <p>• "错误的登出 (GET)" 链接使用GET方法，会报404错误</p>
        </div>
    </div>
</body>
</html>
