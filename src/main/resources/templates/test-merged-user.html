<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试合并用户功能</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        .success {
            border-left-color: #4CAF50;
            background-color: #e8f5e8;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #1976D2;
        }
        .btn-success {
            background-color: #4CAF50;
        }
        .btn-success:hover {
            background-color: #45a049;
        }
        .logout-btn {
            background-color: #f44336;
            float: right;
        }
        .logout-btn:hover {
            background-color: #da190b;
        }
        .user-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <form th:action="@{/logout}" method="post" style="float: right; margin: 0;">
            <button type="submit" class="btn logout-btn">退出登录</button>
        </form>
        <div style="clear: both;"></div>
        
        <h1>🎉 用户表合并功能测试</h1>
        
        <div class="user-info">
            <h3>当前登录用户信息</h3>
            <p><strong>用户名:</strong> <span sec:authentication="name">用户</span></p>
            <p><strong>角色:</strong> <span sec:authentication="principal.authorities">角色</span></p>
            <p><strong>显示名:</strong> <span sec:authentication="principal.name">显示名</span></p>
        </div>
        
        <div class="test-section success">
            <h3>✅ 合并成功！</h3>
            <p><strong>原来的设计：</strong></p>
            <ul>
                <li><code>users</code> 表：存储用户基本信息（姓名、邮箱、手机号）</li>
                <li><code>app_users</code> 表：存储认证信息（用户名、密码、角色）</li>
            </ul>
            
            <p><strong>现在的设计：</strong></p>
            <ul>
                <li><code>users</code> 表：统一存储所有信息（基本信息 + 认证信息）</li>
                <li>新增字段：<code>username</code>、<code>password</code>、<code>role</code>、<code>enabled</code></li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🔧 功能测试</h3>
            <p>以下功能现在都使用同一张 <code>users</code> 表：</p>
            
            <div style="margin: 20px 0;">
                <a href="/users" class="btn">用户管理页面</a>
                <a href="/users/new" class="btn btn-success">创建新用户</a>
            </div>
            
            <p><strong>测试步骤：</strong></p>
            <ol>
                <li>点击"创建新用户"，填写完整信息（包括用户名和密码）</li>
                <li>创建成功后，退出登录</li>
                <li>使用新创建的用户名和密码登录</li>
                <li>验证新用户可以正常登录和使用系统</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>📊 数据库结构变化</h3>
            <div class="code">
                <strong>新的 users 表结构：</strong><br>
                - id (主键)<br>
                - name (姓名)<br>
                - email (邮箱，唯一)<br>
                - phone (手机号)<br>
                - username (用户名，唯一) ← 新增<br>
                - password (密码，加密) ← 新增<br>
                - role (角色：USER/ADMIN) ← 新增<br>
                - enabled (是否启用) ← 新增<br>
                - created_at (创建时间)<br>
                - updated_at (更新时间)
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔐 认证功能</h3>
            <p><strong>现在的认证流程：</strong></p>
            <ul>
                <li>用户登录时使用 <code>username</code> 字段进行认证</li>
                <li>密码使用 BCrypt 加密存储在 <code>password</code> 字段</li>
                <li>角色权限通过 <code>role</code> 字段管理</li>
                <li>账户状态通过 <code>enabled</code> 字段控制</li>
            </ul>
            
            <p><strong>默认行为：</strong></p>
            <ul>
                <li>如果创建用户时未指定用户名，自动使用邮箱作为用户名</li>
                <li>如果未指定密码，使用默认密码 "123456"</li>
                <li>新用户默认角色为 USER</li>
                <li>新用户默认状态为启用</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="/" class="btn">返回首页</a>
            <a href="/users" class="btn">用户管理</a>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background-color: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;">
            <h4 style="margin-top: 0; color: #856404;">💡 提示</h4>
            <p style="margin-bottom: 0; color: #856404;">
                现在创建的每个用户都可以用来登录系统！用户管理和认证功能已完全统一。
            </p>
        </div>
    </div>
</body>
</html>
