<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录成功</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .success-icon {
            font-size: 64px;
            color: #4CAF50;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .user-info {
            background-color: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: left;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px;
        }
        .btn:hover {
            background-color: #1976D2;
        }
        .logout-btn {
            background-color: #f44336;
        }
        .logout-btn:hover {
            background-color: #da190b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✅</div>
        <h1>登录成功！</h1>
        
        <div class="user-info">
            <h3>用户信息</h3>
            <p><strong>用户名:</strong> <span sec:authentication="name">用户</span></p>
            <p><strong>角色:</strong> <span sec:authentication="principal.authorities">角色</span></p>
            <p><strong>登录时间:</strong> <span th:text="${#dates.format(#dates.createNow(), 'yyyy-MM-dd HH:mm:ss')}">时间</span></p>
        </div>
        
        <div>
            <a href="/" class="btn">返回首页</a>
            <a href="/users" class="btn">用户管理</a>
            <a href="/logout" class="btn logout-btn">退出登录</a>
        </div>
        
        <div style="margin-top: 30px; color: #666;">
            <p>您现在可以访问所有受保护的页面和功能。</p>
        </div>
    </div>
</body>
</html>
