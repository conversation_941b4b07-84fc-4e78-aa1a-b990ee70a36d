<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - Java Web应用</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .login-header p {
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .login-btn:active {
            transform: translateY(0);
        }
        
        .alert {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .alert-error {
            background-color: #fee;
            border: 1px solid #fcc;
            color: #c33;
        }
        
        .alert-success {
            background-color: #efe;
            border: 1px solid #cfc;
            color: #3c3;
        }
        
        .demo-info {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .demo-info h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .demo-info p {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .demo-info code {
            background-color: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>用户登录</h1>
            <p>请输入您的用户名和密码</p>
        </div>
        
        <!-- 错误消息 -->
        <div th:if="${error}" class="alert alert-error">
            <span th:text="${error}">错误信息</span>
        </div>
        
        <!-- 成功消息 -->
        <div th:if="${message}" class="alert alert-success">
            <span th:text="${message}">成功信息</span>
        </div>
        
        <!-- 登录表单 -->
        <form th:action="@{/login}" method="post">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" 
                       id="username" 
                       name="username" 
                       required 
                       placeholder="请输入用户名"
                       autocomplete="username">
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" 
                       id="password" 
                       name="password" 
                       required 
                       placeholder="请输入密码"
                       autocomplete="current-password">
            </div>
            
            <button type="submit" class="login-btn">登录</button>
        </form>
        
        <!-- 演示信息 -->
        <div class="demo-info">
            <h4>演示账户信息</h4>
            <p><strong>用户名:</strong> <code>admin</code></p>
            <p><strong>密码:</strong> <code>admin123</code></p>
            <p style="margin-top: 10px; font-size: 12px; color: #888;">
                这是一个演示账户，首次启动时会自动创建。
            </p>
        </div>
        
        <div class="footer">
            <p>&copy; 2025 Java Web应用演示项目</p>
        </div>
    </div>
</body>
</html>
