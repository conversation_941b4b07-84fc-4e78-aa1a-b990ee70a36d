<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - Java Web应用</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-floating .form-control {
            border-radius: 10px;
            border: 2px solid #e1e5e9;
        }
        .form-floating .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .demo-card {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border: none;
            border-radius: 10px;
        }
        .brand-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- 登录头部 -->
            <div class="login-header">
                <i class="bi bi-code-square brand-icon"></i>
                <h2 class="mb-2">欢迎回来</h2>
                <p class="mb-0">请登录您的账户</p>
            </div>
            
            <!-- 登录表单 -->
            <div class="login-body">
                <!-- 错误信息 -->
                <div th:if="${param.error}" class="alert alert-danger d-flex align-items-center" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <div>
                        用户名或密码错误，请重试
                    </div>
                </div>
                
                <!-- 登出成功信息 -->
                <div th:if="${param.logout}" class="alert alert-success d-flex align-items-center" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <div>
                        您已成功退出登录
                    </div>
                </div>
                
                <form th:action="@{/login}" method="post">
                    <div class="form-floating mb-3">
                        <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required>
                        <label for="username">
                            <i class="bi bi-person me-2"></i>用户名
                        </label>
                    </div>
                    
                    <div class="form-floating mb-4">
                        <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                        <label for="password">
                            <i class="bi bi-lock me-2"></i>密码
                        </label>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-login btn-lg">
                            <i class="bi bi-box-arrow-in-right me-2"></i>
                            登录
                        </button>
                    </div>
                </form>
                
                <!-- 演示信息 -->
                <div class="card demo-card mt-4">
                    <div class="card-body">
                        <h6 class="card-title d-flex align-items-center">
                            <i class="bi bi-info-circle me-2"></i>
                            演示账户
                        </h6>
                        <div class="row g-2">
                            <div class="col-12">
                                <small class="text-muted">管理员账户：</small>
                                <div class="d-flex justify-content-between align-items-center">
                                    <code class="small">admin / admin123</code>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="fillDemo('admin', 'admin123')">
                                        <i class="bi bi-clipboard"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 系统信息 -->
                <div class="text-center mt-4">
                    <small class="text-muted">
                        <i class="bi bi-shield-check me-1"></i>
                        基于Spring Security的安全认证
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 填充演示账户信息
        function fillDemo(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            
            // 添加视觉反馈
            const btn = event.target.closest('button');
            const originalContent = btn.innerHTML;
            btn.innerHTML = '<i class="bi bi-check"></i>';
            btn.classList.remove('btn-outline-primary');
            btn.classList.add('btn-success');
            
            setTimeout(() => {
                btn.innerHTML = originalContent;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline-primary');
            }, 1000);
        }
        
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            // 自动聚焦到用户名输入框
            const usernameInput = document.getElementById('username');
            if (usernameInput && !usernameInput.value) {
                usernameInput.focus();
            }
            
            // 添加回车键快捷登录
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const form = document.querySelector('form');
                    if (form) {
                        form.submit();
                    }
                }
            });
        });
        
        // 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                const forms = document.getElementsByClassName('needs-validation');
                const validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>
