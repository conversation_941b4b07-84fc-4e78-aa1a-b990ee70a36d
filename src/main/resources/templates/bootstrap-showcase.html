<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bootstrap风格展示</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: box-shadow 0.15s ease-in-out;
        }
        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .comparison-table th {
            background-color: #f8f9fa;
            border-top: none;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-code-square"></i>
                Java Web应用
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="bi bi-house"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/users">
                            <i class="bi bi-people"></i> 用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/bootstrap-showcase">
                            <i class="bi bi-palette"></i> Bootstrap展示
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-flask"></i> 测试功能
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/test-merged-user">合并功能测试</a></li>
                            <li><a class="dropdown-item" href="/test-update-user">更新功能测试</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/hello">问候页面</a></li>
                        </ul>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            <span sec:authentication="name">用户</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">用户信息</h6></li>
                            <li><span class="dropdown-item-text">角色: <span sec:authentication="principal.authorities">角色</span></span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form th:action="@{/logout}" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="bi bi-box-arrow-right"></i> 退出登录
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <div class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">Bootstrap风格界面</h1>
                    <p class="lead mb-4">现代化、响应式的用户界面设计，基于Bootstrap 5.3框架</p>
                    <div class="d-flex gap-3">
                        <a href="/users" class="btn btn-light btn-lg">
                            <i class="bi bi-people"></i> 体验用户管理
                        </a>
                        <a href="/classic" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-arrow-left"></i> 返回原版
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="bi bi-bootstrap feature-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
        <!-- 特性介绍 -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-center mb-4">Bootstrap风格特性</h2>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="bi bi-phone text-primary" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">响应式设计</h5>
                        <p class="card-text">完美适配桌面、平板和移动设备，提供一致的用户体验。</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="bi bi-palette text-success" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">现代化组件</h5>
                        <p class="card-text">使用Bootstrap最新组件，包括导航栏、卡片、表单等。</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <i class="bi bi-speedometer2 text-warning" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">高性能</h5>
                        <p class="card-text">优化的CSS和JavaScript，快速加载和流畅交互。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能对比 -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-table"></i> 界面对比
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover comparison-table">
                                <thead>
                                    <tr>
                                        <th>功能模块</th>
                                        <th>原版本</th>
                                        <th>Bootstrap版本</th>
                                        <th>改进</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>首页</strong></td>
                                        <td>简单的CSS样式</td>
                                        <td>Bootstrap组件 + 响应式布局</td>
                                        <td><span class="badge bg-success">大幅提升</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>导航</strong></td>
                                        <td>无导航栏</td>
                                        <td>Bootstrap导航栏 + 下拉菜单</td>
                                        <td><span class="badge bg-primary">全新功能</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>用户列表</strong></td>
                                        <td>基础表格</td>
                                        <td>响应式表格 + 搜索筛选</td>
                                        <td><span class="badge bg-success">大幅提升</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>表单</strong></td>
                                        <td>简单输入框</td>
                                        <td>浮动标签 + 验证反馈</td>
                                        <td><span class="badge bg-success">大幅提升</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>登录页面</strong></td>
                                        <td>基础样式</td>
                                        <td>现代化卡片设计</td>
                                        <td><span class="badge bg-success">大幅提升</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速体验 -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-rocket"></i> 快速体验
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">点击下面的按钮体验不同版本的界面：</p>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <h6>Bootstrap版本（当前）</h6>
                                <div class="d-grid gap-2">
                                    <a href="/" class="btn btn-primary">
                                        <i class="bi bi-house"></i> Bootstrap首页
                                    </a>
                                    <a href="/users" class="btn btn-success">
                                        <i class="bi bi-people"></i> Bootstrap用户管理
                                    </a>
                                    <a href="/users/new" class="btn btn-info">
                                        <i class="bi bi-person-plus"></i> Bootstrap用户表单
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>原版本</h6>
                                <div class="d-grid gap-2">
                                    <a href="/classic" class="btn btn-outline-secondary">
                                        <i class="bi bi-house"></i> 原版首页
                                    </a>
                                    <a href="/users/classic" class="btn btn-outline-secondary">
                                        <i class="bi bi-people"></i> 原版用户管理
                                    </a>
                                    <a href="/test-merged-user" class="btn btn-outline-secondary">
                                        <i class="bi bi-flask"></i> 功能测试
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术栈 -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-gear"></i> 技术栈
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>前端技术</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Bootstrap 5.3
                                        <span class="badge bg-primary rounded-pill">CSS框架</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Bootstrap Icons
                                        <span class="badge bg-secondary rounded-pill">图标库</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Thymeleaf
                                        <span class="badge bg-success rounded-pill">模板引擎</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>后端技术</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Spring Boot 3.2
                                        <span class="badge bg-success rounded-pill">框架</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Spring Security
                                        <span class="badge bg-warning text-dark rounded-pill">安全</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        MySQL + JPA
                                        <span class="badge bg-info rounded-pill">数据库</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>Bootstrap风格演示</h6>
                    <p class="mb-0">基于Bootstrap 5.3的现代化界面设计</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">响应式 • 现代化 • 易用性</p>
                    <p class="mb-0">© 2025 Java Web应用</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
