<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title}">Java Web应用</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .stat-card {
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: box-shadow 0.15s ease-in-out;
        }
        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-code-square"></i>
                Java Web应用
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">
                            <i class="bi bi-house"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/users">
                            <i class="bi bi-people"></i> 用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/roles">
                            <i class="bi bi-shield-check"></i> 角色管理
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-flask"></i> 测试功能
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/test-merged-user">合并功能测试</a></li>
                            <li><a class="dropdown-item" href="/test-update-user">更新功能测试</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/hello">问候页面</a></li>
                        </ul>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            <span sec:authentication="name">用户</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">用户信息</h6></li>
                            <li><span class="dropdown-item-text">角色: <span sec:authentication="principal.authorities">角色</span></span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form th:action="@{/logout}" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="bi bi-box-arrow-right"></i> 退出登录
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <div class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3" th:text="${title}">欢迎使用Java Web应用</h1>
                    <p class="lead mb-4" th:text="${message}">这是一个基于Spring Boot的演示应用，已连接MySQL数据库</p>
                    <div class="d-flex gap-3">
                        <a href="/users" class="btn btn-light btn-lg">
                            <i class="bi bi-people"></i> 用户管理
                        </a>
                        <a href="/users/new" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-person-plus"></i> 新增用户
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="bi bi-server feature-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
        <!-- 统计卡片 -->
        <div class="row mb-5">
            <div class="col-md-4 mb-3">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-people text-primary" style="font-size: 2.5rem;"></i>
                        <h3 class="card-title mt-3" th:text="${userCount}">0</h3>
                        <p class="card-text text-muted">总用户数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-database text-success" style="font-size: 2.5rem;"></i>
                        <h3 class="card-title mt-3">MySQL</h3>
                        <p class="card-text text-muted" th:text="${dbStatus}">数据库状态</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-shield-check text-warning" style="font-size: 2.5rem;"></i>
                        <h3 class="card-title mt-3">安全</h3>
                        <p class="card-text text-muted">Spring Security</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能特性 -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-center mb-4">系统功能</h2>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-person-gear text-primary" style="font-size: 2.5rem;"></i>
                        <h5 class="card-title mt-3">用户管理</h5>
                        <p class="card-text">完整的用户CRUD操作，包括创建、查看、编辑和删除用户功能。</p>
                        <a href="/users" class="btn btn-primary">
                            <i class="bi bi-arrow-right"></i> 进入管理
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-shield-lock text-success" style="font-size: 2.5rem;"></i>
                        <h5 class="card-title mt-3">安全认证</h5>
                        <p class="card-text">基于Spring Security的用户认证和授权，支持角色权限管理。</p>
                        <a href="/test-merged-user" class="btn btn-success">
                            <i class="bi bi-arrow-right"></i> 功能测试
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-api text-info" style="font-size: 2.5rem;"></i>
                        <h5 class="card-title mt-3">API接口</h5>
                        <p class="card-text">RESTful API设计，支持JSON数据交互和前后端分离开发。</p>
                        <a href="/users/api" class="btn btn-info text-white">
                            <i class="bi bi-arrow-right"></i> 查看API
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速链接 -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-link-45deg"></i> 快速链接
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>功能页面</h6>
                                <div class="list-group list-group-flush">
                                    <a href="/users" class="list-group-item list-group-item-action">
                                        <i class="bi bi-people"></i> 用户管理
                                    </a>
                                    <a href="/users/new" class="list-group-item list-group-item-action">
                                        <i class="bi bi-person-plus"></i> 新增用户
                                    </a>
                                    <a href="/hello" class="list-group-item list-group-item-action">
                                        <i class="bi bi-chat-dots"></i> 问候页面
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>API接口</h6>
                                <div class="list-group list-group-flush">
                                    <a href="/api/hello" class="list-group-item list-group-item-action">
                                        <i class="bi bi-code"></i> Hello API
                                    </a>
                                    <a href="/users/api" class="list-group-item list-group-item-action">
                                        <i class="bi bi-database"></i> 用户列表API
                                    </a>
                                    <a href="/test/count" class="list-group-item list-group-item-action">
                                        <i class="bi bi-bar-chart"></i> 统计API
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>Java Web应用演示项目</h6>
                    <p class="mb-0">基于Spring Boot + MySQL + Bootstrap构建</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">服务器运行在端口: 8080</p>
                    <p class="mb-0">© 2025 演示项目</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
