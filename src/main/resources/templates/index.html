<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title}">Java Web应用</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .message {
            background-color: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #2196F3;
            margin: 20px 0;
        }
        .links {
            margin-top: 30px;
        }
        .links a {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .links a:hover {
            background-color: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 th:text="${title}">欢迎使用Java Web应用</h1>
        
        <div class="message">
            <p th:text="${message}">这是一个基于Spring Boot的演示应用</p>
        </div>
        
        <div class="links">
            <h3>试试这些链接：</h3>
            <a href="/hello">问候页面</a>
            <a href="/hello?name=张三">带参数的问候</a>
            <a href="/api/hello">API接口</a>
            <a href="/api/hello?name=李四">带参数的API</a>
        </div>
        
        <div style="margin-top: 30px; text-align: center; color: #666;">
            <p>这是一个Spring Boot演示项目</p>
            <p>服务器运行在端口: 8080</p>
        </div>
    </div>
</body>
</html>
