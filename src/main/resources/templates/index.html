<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title}">Java Web应用</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .message {
            background-color: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #2196F3;
            margin: 20px 0;
        }
        .links {
            margin-top: 30px;
        }
        .links a {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .links a:hover {
            background-color: #1976D2;
        }
        .user-info {
            background-color: #f0f8ff;
            padding: 15px;
            border-left: 4px solid #4CAF50;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .logout-btn {
            background-color: #f44336;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            float: right;
        }
        .logout-btn:hover {
            background-color: #da190b;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 用户信息和登出 -->
        <div class="user-info">
            <a href="/logout" class="logout-btn">退出登录</a>
            <p><strong>欢迎您，</strong> <span sec:authentication="name">用户</span>!</p>
            <p><strong>角色：</strong> <span sec:authentication="principal.authorities">角色</span></p>
            <div style="clear: both;"></div>
        </div>

        <h1 th:text="${title}">欢迎使用Java Web应用</h1>
        
        <div class="message">
            <p th:text="${message}">这是一个基于Spring Boot的演示应用</p>
        </div>

        <div class="message" style="background-color: #e8f5e8; border-left-color: #4CAF50;">
            <h4>数据库状态</h4>
            <p th:text="${dbStatus}">数据库连接状态</p>
            <p>当前用户数量: <span th:text="${userCount}">0</span> 人</p>
        </div>
        
        <div class="links">
            <h3>功能页面：</h3>
            <a href="/users">用户管理</a>
            <a href="/hello">问候页面</a>
            <a href="/hello?name=张三">带参数的问候</a>
        </div>

        <div class="links">
            <h3>API接口：</h3>
            <a href="/api/hello">Hello API</a>
            <a href="/api/hello?name=李四">带参数的API</a>
            <a href="/users/api">用户列表API</a>
        </div>
        
        <div style="margin-top: 30px; text-align: center; color: #666;">
            <p>这是一个Spring Boot演示项目</p>
            <p>服务器运行在端口: 8080</p>
        </div>
    </div>
</body>
</html>
