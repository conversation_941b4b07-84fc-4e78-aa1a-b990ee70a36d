<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代化UI展示</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        /* 主容器 */
        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏 */
        .sidebar {
            width: 240px;
            background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 2px 0 8px rgba(0,0,0,0.1);
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar-header h2 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .sidebar-header .subtitle {
            font-size: 12px;
            opacity: 0.8;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-left-color: #60a5fa;
        }

        .nav-link.active {
            background-color: rgba(255,255,255,0.15);
            color: white;
            border-left-color: #3b82f6;
        }

        .nav-link i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            margin-left: 240px;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航 */
        .top-navbar {
            background: white;
            padding: 0 30px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border-bottom: 1px solid #e5e7eb;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            color: #6b7280;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #3b82f6;
            text-decoration: none;
        }

        .breadcrumb .separator {
            margin: 0 8px;
            color: #d1d5db;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .logout-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .logout-btn:hover {
            background: #dc2626;
        }

        /* 页面内容 */
        .page-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .showcase-section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }

        .feature-card.success {
            border-left-color: #10b981;
        }

        .feature-card.warning {
            border-left-color: #f59e0b;
        }

        .feature-card.info {
            border-left-color: #8b5cf6;
        }

        .feature-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #1f2937;
        }

        .feature-desc {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.5;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .comparison-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        .comparison-table tr:hover {
            background: #f9fafb;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-new {
            background: #dcfce7;
            color: #166534;
        }

        .status-improved {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 16px 0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .main-content {
                margin-left: 0;
            }

            .top-navbar {
                padding: 0 15px;
            }

            .page-content {
                padding: 20px 15px;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2>管理系统</h2>
                <div class="subtitle">Java Web Application</div>
            </div>
            
            <div class="sidebar-nav">
                <div class="nav-item">
                    <a href="/" class="nav-link">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="/users/modern" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="/ui-showcase" class="nav-link active">
                        <i class="fas fa-palette"></i>
                        <span>UI展示</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="/test-merged-user" class="nav-link">
                        <i class="fas fa-flask"></i>
                        <span>功能测试</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航 -->
            <header class="top-navbar">
                <div class="breadcrumb">
                    <a href="/">首页</a>
                    <span class="separator">/</span>
                    <span>UI展示</span>
                </div>
                
                <div class="user-menu">
                    <div class="user-info">
                        <div class="user-avatar" th:text="${#strings.substring(#authentication.name, 0, 1).toUpperCase()}">A</div>
                        <span sec:authentication="name">用户</span>
                        <span class="text-muted">|</span>
                        <span sec:authentication="principal.authorities" style="font-size: 12px; color: #6b7280;">角色</span>
                    </div>
                    
                    <form th:action="@{/logout}" method="post" style="margin: 0;">
                        <button type="submit" class="logout-btn">
                            <i class="fas fa-sign-out-alt"></i> 退出
                        </button>
                    </form>
                </div>
            </header>

            <!-- 页面内容 -->
            <main class="page-content">
                <!-- 介绍部分 -->
                <div class="showcase-section">
                    <h1 class="section-title">
                        <i class="fas fa-palette"></i>
                        现代化UI界面展示
                    </h1>
                    <p style="color: #6b7280; margin-bottom: 20px;">
                        参考您提供的Omall风格，我们重新设计了整个管理系统的界面，采用现代化的设计语言和用户体验。
                    </p>
                    
                    <div class="feature-grid">
                        <div class="feature-card success">
                            <div class="feature-title">🎨 现代化设计</div>
                            <div class="feature-desc">采用深蓝色渐变侧边栏、清爽的卡片式布局和现代化的色彩方案</div>
                        </div>
                        
                        <div class="feature-card info">
                            <div class="feature-title">📱 响应式布局</div>
                            <div class="feature-desc">完美适配桌面端和移动端，提供一致的用户体验</div>
                        </div>
                        
                        <div class="feature-card warning">
                            <div class="feature-title">⚡ 交互优化</div>
                            <div class="feature-desc">流畅的动画效果、悬停状态和视觉反馈</div>
                        </div>
                    </div>
                </div>

                <!-- 功能对比 -->
                <div class="showcase-section">
                    <h2 class="section-title">
                        <i class="fas fa-chart-bar"></i>
                        界面优化对比
                    </h2>
                    
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>功能模块</th>
                                <th>原版本</th>
                                <th>现代化版本</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>首页</td>
                                <td>简单的欢迎页面</td>
                                <td>统计卡片 + 快捷操作 + 渐变背景</td>
                                <td><span class="status-badge status-new">全新设计</span></td>
                            </tr>
                            <tr>
                                <td>用户列表</td>
                                <td>基础表格样式</td>
                                <td>现代化表格 + 搜索筛选 + 用户头像</td>
                                <td><span class="status-badge status-improved">大幅优化</span></td>
                            </tr>
                            <tr>
                                <td>侧边栏</td>
                                <td>无侧边栏</td>
                                <td>深蓝色渐变 + 图标导航 + 活跃状态</td>
                                <td><span class="status-badge status-new">全新设计</span></td>
                            </tr>
                            <tr>
                                <td>顶部导航</td>
                                <td>简单的用户信息</td>
                                <td>面包屑导航 + 用户头像 + 现代化按钮</td>
                                <td><span class="status-badge status-improved">大幅优化</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 快速访问 -->
                <div class="showcase-section">
                    <h2 class="section-title">
                        <i class="fas fa-rocket"></i>
                        快速体验
                    </h2>
                    <p style="color: #6b7280; margin-bottom: 20px;">
                        点击下面的按钮体验不同的界面版本：
                    </p>
                    
                    <div style="display: flex; gap: 16px; flex-wrap: wrap;">
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-home"></i>
                            现代化首页
                        </a>
                        
                        <a href="/users/modern" class="btn btn-success">
                            <i class="fas fa-users"></i>
                            现代化用户管理
                        </a>
                        
                        <a href="/classic" class="btn btn-warning">
                            <i class="fas fa-history"></i>
                            传统版首页
                        </a>
                        
                        <a href="/users" class="btn btn-warning">
                            <i class="fas fa-list"></i>
                            传统版用户列表
                        </a>
                    </div>
                </div>

                <!-- 技术特性 -->
                <div class="showcase-section">
                    <h2 class="section-title">
                        <i class="fas fa-cogs"></i>
                        技术实现
                    </h2>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="feature-title">CSS Grid & Flexbox</div>
                            <div class="feature-desc">使用现代CSS布局技术，实现灵活的响应式设计</div>
                        </div>
                        
                        <div class="feature-card success">
                            <div class="feature-title">Font Awesome 图标</div>
                            <div class="feature-desc">集成丰富的图标库，提升界面的视觉表现力</div>
                        </div>
                        
                        <div class="feature-card warning">
                            <div class="feature-title">CSS 变量与渐变</div>
                            <div class="feature-desc">使用CSS自定义属性和渐变效果，创造现代化视觉体验</div>
                        </div>
                        
                        <div class="feature-card info">
                            <div class="feature-title">JavaScript 交互</div>
                            <div class="feature-desc">添加流畅的交互效果和动态数据加载</div>
                        </div>
                    </div>

                    <div class="code-block">
/* 现代化设计的核心CSS */
.sidebar {
    background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
    box-shadow: 2px 0 8px rgba(0,0,0,0.1);
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    border-left-color: #60a5fa;
    transition: all 0.3s ease;
}
                    </div>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
