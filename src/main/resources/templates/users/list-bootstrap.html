<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>
    <title>用户管理</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #007bff, #6f42c1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        .table th {
            border-top: none;
            font-weight: 600;
            color: #495057;
            background-color: #f8f9fa;
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .status-badge {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-code-square"></i>
                Java Web应用
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="bi bi-house"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/users">
                            <i class="bi bi-people"></i> 用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/roles">
                            <i class="bi bi-shield-check"></i> 角色管理
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-flask"></i> 测试功能
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/test-merged-user">合并功能测试</a></li>
                            <li><a class="dropdown-item" href="/test-update-user">更新功能测试</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/hello">问候页面</a></li>
                        </ul>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            <span sec:authentication="name">用户</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">用户信息</h6></li>
                            <li><span class="dropdown-item-text">角色: <span sec:authentication="principal.authorities">角色</span></span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form th:action="@{/logout}" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="bi bi-box-arrow-right"></i> 退出登录
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 页面标题和操作 -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">
                            <i class="bi bi-people"></i> 用户管理
                        </h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="/">首页</a></li>
                                <li class="breadcrumb-item active">用户管理</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="/users/new" class="btn btn-primary">
                            <i class="bi bi-person-plus"></i> 新增用户
                        </a>
                        <button class="btn btn-outline-secondary" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="card mb-4">
            <div class="card-body">
                <form class="row g-3" id="searchForm">
                    <div class="col-md-4">
                        <label for="searchInput" class="form-label">搜索用户</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control" id="searchInput" placeholder="输入姓名或邮箱">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="roleFilter" class="form-label">角色筛选</label>
                        <select class="form-select" id="roleFilter">
                            <option value="">全部角色</option>
                            <option value="USER">普通用户</option>
                            <option value="ADMIN">管理员</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="statusFilter" class="form-label">状态筛选</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="true">启用</option>
                            <option value="false">禁用</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="applyFilters()">
                                <i class="bi bi-funnel"></i> 筛选
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 用户列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i> 用户列表
                </h5>
                <span class="badge bg-primary">
                    共 <span th:text="${users.size()}">0</span> 个用户
                </span>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">用户信息</th>
                                <th scope="col">用户名</th>
                                <th scope="col">手机号</th>
                                <th scope="col">角色</th>
                                <th scope="col">状态</th>
                                <th scope="col">创建时间</th>
                                <th scope="col">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="user, iterStat : ${users}">
                                <td th:text="${iterStat.count}">1</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-3" th:text="${#strings.substring(user.name, 0, 1).toUpperCase()}">U</div>
                                        <div>
                                            <div class="fw-bold" th:text="${user.name}">用户名</div>
                                            <small class="text-muted" th:text="${user.email}"><EMAIL></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <code th:text="${user.username}">username</code>
                                </td>
                                <td th:text="${user.phone ?: '未设置'}">手机号</td>
                                <td>
                                    <span class="badge" th:classappend="${user.role?.name() == 'ADMIN'} ? 'bg-warning text-dark' : 'bg-info'">
                                        <i class="bi" th:classappend="${user.role?.name() == 'ADMIN'} ? 'bi-shield-fill' : 'bi-person'"></i>
                                        <span th:if="${user.role?.name() == 'ADMIN'}">管理员</span>
                                        <span th:if="${user.role?.name() == 'USER'}">普通用户</span>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge status-badge" th:classappend="${user.enabled} ? 'bg-success' : 'bg-danger'">
                                        <i class="bi" th:classappend="${user.enabled} ? 'bi-check-circle' : 'bi-x-circle'"></i>
                                        <span th:text="${user.enabled} ? '启用' : '禁用'">状态</span>
                                    </span>
                                </td>
                                <td>
                                    <small th:text="${#temporals.format(user.createdAt, 'yyyy-MM-dd HH:mm')}">创建时间</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a th:href="@{/users/{id}(id=${user.id})}" class="btn btn-outline-info btn-sm" title="查看详情">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a th:href="@{/users/{id}/edit(id=${user.id})}" class="btn btn-outline-warning btn-sm" title="编辑">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button class="btn btn-outline-danger btn-sm" th:onclick="'deleteUser(' + ${user.id} + ')'" title="删除">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div th:if="${users.empty}" class="text-center py-5">
            <i class="bi bi-people" style="font-size: 4rem; color: #dee2e6;"></i>
            <h4 class="text-muted mt-3">暂无用户数据</h4>
            <p class="text-muted">点击上方"新增用户"按钮创建第一个用户</p>
            <a href="/users/new" class="btn btn-primary">
                <i class="bi bi-person-plus"></i> 新增用户
            </a>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 删除用户功能
        function deleteUser(userId) {
            if (confirm('确定要删除这个用户吗？此操作不可撤销。')) {
                const csrfToken = document.querySelector('meta[name="_csrf"]')?.getAttribute('content');
                const csrfHeader = document.querySelector('meta[name="_csrf_header"]')?.getAttribute('content');
                
                const headers = {
                    'Content-Type': 'application/json',
                };
                
                if (csrfToken && csrfHeader) {
                    headers[csrfHeader] = csrfToken;
                }
                
                fetch('/users/api/' + userId, {
                    method: 'DELETE',
                    headers: headers
                })
                .then(response => {
                    if (response.ok) {
                        // 显示成功提示
                        showAlert('用户删除成功！', 'success');
                        // 延迟刷新页面
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert('删除失败，请重试', 'danger');
                    }
                })
                .catch(error => {
                    console.error('删除失败:', error);
                    showAlert('删除失败，请重试', 'danger');
                });
            }
        }

        // 显示提示信息
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 3000);
        }

        // 应用筛选
        function applyFilters() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const roleFilter = document.getElementById('roleFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            
            const rows = document.querySelectorAll('tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const userName = row.querySelector('.fw-bold').textContent.toLowerCase();
                const userEmail = row.querySelector('.text-muted').textContent.toLowerCase();
                const userRole = row.querySelector('.badge').textContent.trim();
                const userStatus = row.querySelector('.status-badge').textContent.trim();
                
                let showRow = true;
                
                // 搜索筛选
                if (searchTerm && !userName.includes(searchTerm) && !userEmail.includes(searchTerm)) {
                    showRow = false;
                }
                
                // 角色筛选
                if (roleFilter && !userRole.includes(roleFilter === 'ADMIN' ? '管理员' : '普通用户')) {
                    showRow = false;
                }
                
                // 状态筛选
                if (statusFilter && !userStatus.includes(statusFilter === 'true' ? '启用' : '禁用')) {
                    showRow = false;
                }
                
                row.style.display = showRow ? '' : 'none';
                if (showRow) visibleCount++;
            });
            
            // 更新计数
            document.querySelector('.badge.bg-primary').innerHTML = `共 ${visibleCount} 个用户`;
        }

        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                applyFilters();
            }
        });

        // 实时搜索
        document.getElementById('searchInput').addEventListener('input', applyFilters);
        document.getElementById('roleFilter').addEventListener('change', applyFilters);
        document.getElementById('statusFilter').addEventListener('change', applyFilters);
    </script>
</body>
</html>
