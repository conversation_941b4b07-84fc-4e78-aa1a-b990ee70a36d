<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${isEdit} ? '编辑用户' : '添加用户'">用户表单</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="email"], input[type="tel"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #2196F3;
            box-shadow: 0 0 5px rgba(33, 150, 243, 0.3);
        }
        .required {
            color: red;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #1976D2;
        }
        .btn-success {
            background-color: #4CAF50;
        }
        .btn-success:hover {
            background-color: #45a049;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        .form-actions {
            margin-top: 30px;
            text-align: center;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #2196F3;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .logout-btn {
            background-color: #f44336;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            float: right;
        }
        .logout-btn:hover {
            background-color: #da190b;
        }
        .error {
            color: #f44336;
            font-size: 14px;
            margin-top: 5px;
        }
        .help-text {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/users" class="back-link">← 返回用户列表</a>
        <form th:action="@{/logout}" method="post" style="float: right; margin: 0;">
            <button type="submit" class="logout-btn" style="border: none; cursor: pointer;">退出登录</button>
        </form>
        <div style="clear: both; margin-bottom: 10px;"></div>
        
        <h1 th:text="${isEdit} ? '编辑用户' : '添加新用户'">用户表单</h1>

        <!-- 错误消息显示 -->
        <div th:if="${error}" class="error" style="background-color: #ffebee; border: 1px solid #f44336; padding: 10px; margin-bottom: 20px; border-radius: 5px;">
            <p th:text="${error}" style="margin: 0; color: #f44336;">错误信息</p>
        </div>
        
        <form th:action="${isEdit ? '/users/' + user.id : '/users'}"
              method="POST"
              onsubmit="return submitForm(event)"
              th:data-api-url="${isEdit ? '/users/api/' + user.id : '/users/api'}"
              th:data-method="${isEdit ? 'PUT' : 'POST'}"
            
            <div class="form-group">
                <label for="name">姓名 <span class="required">*</span></label>
                <input type="text"
                       id="name"
                       name="name"
                       th:value="${user?.name}"
                       required
                       maxlength="50"
                       placeholder="请输入用户姓名">
                <div class="help-text">最多50个字符</div>
            </div>

            <div class="form-group">
                <label for="email">邮箱 <span class="required">*</span></label>
                <input type="email"
                       id="email"
                       name="email"
                       th:value="${user?.email}"
                       required
                       maxlength="100"
                       placeholder="请输入邮箱地址">
                <div class="help-text">邮箱地址必须唯一，最多100个字符</div>
            </div>

            <div class="form-group">
                <label for="phone">手机号</label>
                <input type="tel"
                       id="phone"
                       name="phone"
                       th:value="${user?.phone}"
                       maxlength="20"
                       placeholder="请输入手机号（可选）">
                <div class="help-text">可选字段，最多20个字符</div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-success" th:text="${isEdit} ? '更新用户' : '创建用户'">
                    提交
                </button>
                <a href="/users" class="btn btn-secondary">取消</a>
            </div>
        </form>
    </div>

    <script>
        function submitForm(event) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);
            const method = form.getAttribute('data-method') || 'POST';
            const isEdit = method === 'PUT';

            // 构建请求数据
            const userData = {
                name: formData.get('name'),
                email: formData.get('email'),
                phone: formData.get('phone') || null
            };

            // 发送请求
            const url = form.getAttribute('data-api-url');
            
            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(userData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message || (isEdit ? '用户更新成功' : '用户创建成功'));
                    window.location.href = '/users';
                } else {
                    alert('操作失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('操作失败: ' + error.message);
                // 如果AJAX失败，回退到传统表单提交
                console.log('AJAX失败，回退到传统表单提交');
                form.onsubmit = null; // 移除事件处理器避免循环
                form.submit();
            });

            return false;
        }
        
        // 表单验证
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const nameInput = document.getElementById('name');
            const emailInput = document.getElementById('email');
            
            // 实时验证
            nameInput.addEventListener('blur', function() {
                if (this.value.trim().length === 0) {
                    showError(this, '姓名不能为空');
                } else if (this.value.length > 50) {
                    showError(this, '姓名不能超过50个字符');
                } else {
                    clearError(this);
                }
            });
            
            emailInput.addEventListener('blur', function() {
                if (this.value.trim().length === 0) {
                    showError(this, '邮箱不能为空');
                } else if (!isValidEmail(this.value)) {
                    showError(this, '请输入有效的邮箱地址');
                } else if (this.value.length > 100) {
                    showError(this, '邮箱不能超过100个字符');
                } else {
                    clearError(this);
                }
            });
        });
        
        function showError(input, message) {
            clearError(input);
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            input.parentNode.appendChild(errorDiv);
        }
        
        function clearError(input) {
            const errorDiv = input.parentNode.querySelector('.error');
            if (errorDiv) {
                errorDiv.remove();
            }
        }
        
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
    </script>
</body>
</html>
