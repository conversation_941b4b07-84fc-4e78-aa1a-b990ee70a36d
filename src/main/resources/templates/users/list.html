<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .header-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #1976D2;
        }
        .btn-success {
            background-color: #4CAF50;
        }
        .btn-success:hover {
            background-color: #45a049;
        }
        .btn-danger {
            background-color: #f44336;
        }
        .btn-danger:hover {
            background-color: #da190b;
        }
        .btn-warning {
            background-color: #ff9800;
        }
        .btn-warning:hover {
            background-color: #e68900;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .actions {
            white-space: nowrap;
        }
        .actions a {
            margin-right: 5px;
            padding: 5px 10px;
            font-size: 12px;
        }
        .stats {
            background-color: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #2196F3;
            margin-bottom: 20px;
        }
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #2196F3;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .logout-btn {
            background-color: #f44336;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            float: right;
        }
        .logout-btn:hover {
            background-color: #da190b;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-link">← 返回首页</a>
        <a href="/logout" class="logout-btn">退出登录</a>
        <div style="clear: both; margin-bottom: 20px;"></div>

        <h1>用户管理</h1>
        <p>当前登录用户: <span sec:authentication="name">用户</span></p>
        
        <div class="stats">
            <p><strong>用户总数:</strong> <span th:text="${userCount}">0</span> 人</p>
        </div>
        
        <div class="header-actions">
            <h3>用户列表</h3>
            <a href="/users/new" class="btn btn-success">+ 添加新用户</a>
        </div>
        
        <div th:if="${users.empty}" class="empty-state">
            <h3>暂无用户数据</h3>
            <p>点击上方"添加新用户"按钮创建第一个用户</p>
        </div>
        
        <table th:if="${!users.empty}">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>姓名</th>
                    <th>邮箱</th>
                    <th>手机号</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr th:each="user : ${users}">
                    <td th:text="${user.id}">1</td>
                    <td th:text="${user.name}">张三</td>
                    <td th:text="${user.email}"><EMAIL></td>
                    <td th:text="${user.phone ?: '未设置'}">13800138000</td>
                    <td th:text="${#temporals.format(user.createdAt, 'yyyy-MM-dd HH:mm')}">2023-01-01 12:00</td>
                    <td class="actions">
                        <a th:href="@{/users/{id}(id=${user.id})}" class="btn">查看</a>
                        <a th:href="@{/users/{id}/edit(id=${user.id})}" class="btn btn-warning">编辑</a>
                        <button class="btn btn-danger" 
                                th:onclick="'deleteUser(' + ${user.id} + ')'">删除</button>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <div style="margin-top: 30px; text-align: center;">
            <h4>API 测试链接</h4>
            <a href="/users/api" class="btn" target="_blank">查看用户API</a>
            <a href="/api/hello" class="btn" target="_blank">Hello API</a>
        </div>
    </div>

    <script>
        function deleteUser(userId) {
            if (confirm('确定要删除这个用户吗？此操作不可撤销。')) {
                fetch('/users/api/' + userId, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('用户删除成功');
                        location.reload();
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('删除失败: ' + error.message);
                });
            }
        }
    </script>
</body>
</html>
