<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>
    <title>用户管理</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        /* 主容器 */
        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏 */
        .sidebar {
            width: 240px;
            background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 2px 0 8px rgba(0,0,0,0.1);
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar-header h2 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .sidebar-header .subtitle {
            font-size: 12px;
            opacity: 0.8;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-left-color: #60a5fa;
        }

        .nav-link.active {
            background-color: rgba(255,255,255,0.15);
            color: white;
            border-left-color: #3b82f6;
        }

        .nav-link i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            margin-left: 240px;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航 */
        .top-navbar {
            background: white;
            padding: 0 30px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border-bottom: 1px solid #e5e7eb;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            color: #6b7280;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #3b82f6;
            text-decoration: none;
        }

        .breadcrumb .separator {
            margin: 0 8px;
            color: #d1d5db;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .logout-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .logout-btn:hover {
            background: #dc2626;
        }

        /* 页面内容 */
        .page-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        /* 页面头部 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        /* 搜索和筛选区域 */
        .filters-section {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }

        .filters-row {
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .filter-label {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
        }

        .filter-input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            min-width: 200px;
        }

        .filter-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 数据表格 */
        .table-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .table-stats {
            font-size: 14px;
            color: #6b7280;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background: #f9fafb;
            padding: 12px 16px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
            border-bottom: 1px solid #e5e7eb;
        }

        .data-table td {
            padding: 16px;
            border-bottom: 1px solid #f3f4f6;
            font-size: 14px;
        }

        .data-table tr:hover {
            background: #f9fafb;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        /* 用户头像 */
        .user-cell {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar-small {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 16px;
        }

        .user-details {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-weight: 500;
            color: #1f2937;
        }

        .user-email {
            font-size: 12px;
            color: #6b7280;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-inactive {
            background: #fee2e2;
            color: #991b1b;
        }

        .role-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .role-admin {
            background: #fef3c7;
            color: #92400e;
        }

        .role-user {
            background: #e0e7ff;
            color: #3730a3;
        }

        /* 操作按钮 */
        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
        }

        .btn-edit {
            background: #f59e0b;
            color: white;
        }

        .btn-edit:hover {
            background: #d97706;
        }

        .btn-view {
            background: #10b981;
            color: white;
        }

        .btn-view:hover {
            background: #059669;
        }

        .btn-delete {
            background: #ef4444;
            color: white;
        }

        .btn-delete:hover {
            background: #dc2626;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .main-content {
                margin-left: 0;
            }

            .top-navbar {
                padding: 0 15px;
            }

            .page-content {
                padding: 20px 15px;
            }

            .page-header {
                flex-direction: column;
                gap: 16px;
                align-items: flex-start;
            }

            .filters-row {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-input {
                min-width: auto;
            }

            .data-table {
                font-size: 12px;
            }

            .data-table th,
            .data-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2>管理系统</h2>
                <div class="subtitle">Java Web Application</div>
            </div>
            
            <div class="sidebar-nav">
                <div class="nav-item">
                    <a href="/" class="nav-link">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="/users" class="nav-link active">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="/test-merged-user" class="nav-link">
                        <i class="fas fa-flask"></i>
                        <span>功能测试</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航 -->
            <header class="top-navbar">
                <div class="breadcrumb">
                    <a href="/">首页</a>
                    <span class="separator">/</span>
                    <span>用户管理</span>
                </div>
                
                <div class="user-menu">
                    <div class="user-info">
                        <div class="user-avatar" th:text="${#strings.substring(#authentication.name, 0, 1).toUpperCase()}">A</div>
                        <span sec:authentication="name">用户</span>
                        <span class="text-muted">|</span>
                        <span sec:authentication="principal.authorities" style="font-size: 12px; color: #6b7280;">角色</span>
                    </div>
                    
                    <form th:action="@{/logout}" method="post" style="margin: 0;">
                        <button type="submit" class="logout-btn">
                            <i class="fas fa-sign-out-alt"></i> 退出
                        </button>
                    </form>
                </div>
            </header>

            <!-- 页面内容 -->
            <main class="page-content">
                <!-- 页面头部 -->
                <div class="page-header">
                    <h1 class="page-title">用户管理</h1>
                    <div class="header-actions">
                        <a href="/users/new" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            新增用户
                        </a>
                        <button class="btn btn-outline" onclick="refreshData()">
                            <i class="fas fa-refresh"></i>
                            刷新
                        </button>
                    </div>
                </div>

                <!-- 搜索筛选区域 -->
                <div class="filters-section">
                    <div class="filters-row">
                        <div class="filter-group">
                            <label class="filter-label">搜索用户</label>
                            <input type="text" class="filter-input" placeholder="输入姓名或邮箱搜索" id="searchInput">
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">角色筛选</label>
                            <select class="filter-input" id="roleFilter">
                                <option value="">全部角色</option>
                                <option value="USER">普通用户</option>
                                <option value="ADMIN">管理员</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">状态筛选</label>
                            <select class="filter-input" id="statusFilter">
                                <option value="">全部状态</option>
                                <option value="true">启用</option>
                                <option value="false">禁用</option>
                            </select>
                        </div>
                        <div class="filter-group" style="margin-top: 20px;">
                            <button class="btn btn-primary" onclick="applyFilters()">
                                <i class="fas fa-search"></i>
                                搜索
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-container">
                    <div class="table-header">
                        <h3 class="table-title">用户列表</h3>
                        <div class="table-stats">
                            共 <span th:text="${users.size()}">0</span> 个用户
                        </div>
                    </div>
                    
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>用户信息</th>
                                <th>用户名</th>
                                <th>手机号</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="user, iterStat : ${users}">
                                <td th:text="${iterStat.count}">1</td>
                                <td>
                                    <div class="user-cell">
                                        <div class="user-avatar-small" th:text="${#strings.substring(user.name, 0, 1).toUpperCase()}">U</div>
                                        <div class="user-details">
                                            <div class="user-name" th:text="${user.name}">用户名</div>
                                            <div class="user-email" th:text="${user.email}"><EMAIL></div>
                                        </div>
                                    </div>
                                </td>
                                <td th:text="${user.username}">username</td>
                                <td th:text="${user.phone ?: '未设置'}">手机号</td>
                                <td>
                                    <span class="role-badge" th:classappend="${user.role?.name() == 'ADMIN'} ? 'role-admin' : 'role-user'">
                                        <span th:if="${user.role?.name() == 'ADMIN'}">管理员</span>
                                        <span th:if="${user.role?.name() == 'USER'}">普通用户</span>
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge" th:classappend="${user.enabled} ? 'status-active' : 'status-inactive'">
                                        <i class="fas fa-circle" style="font-size: 8px;"></i>
                                        <span th:text="${user.enabled} ? '启用' : '禁用'">状态</span>
                                    </span>
                                </td>
                                <td th:text="${#temporals.format(user.createdAt, 'yyyy-MM-dd HH:mm')}">创建时间</td>
                                <td>
                                    <div class="action-buttons">
                                        <a th:href="@{/users/{id}(id=${user.id})}" class="btn btn-view btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a th:href="@{/users/{id}/edit(id=${user.id})}" class="btn btn-edit btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-delete btn-sm" th:onclick="'deleteUser(' + ${user.id} + ')'">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </main>
        </div>
    </div>

    <script>
        // 删除用户功能
        function deleteUser(userId) {
            if (confirm('确定要删除这个用户吗？此操作不可撤销。')) {
                const csrfToken = document.querySelector('meta[name="_csrf"]')?.getAttribute('content');
                const csrfHeader = document.querySelector('meta[name="_csrf_header"]')?.getAttribute('content');
                
                const headers = {
                    'Content-Type': 'application/json',
                };
                
                if (csrfToken && csrfHeader) {
                    headers[csrfHeader] = csrfToken;
                }
                
                fetch('/users/api/' + userId, {
                    method: 'DELETE',
                    headers: headers
                })
                .then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('删除失败，请重试');
                    }
                })
                .catch(error => {
                    console.error('删除失败:', error);
                    alert('删除失败，请重试');
                });
            }
        }

        // 刷新数据
        function refreshData() {
            location.reload();
        }

        // 应用筛选
        function applyFilters() {
            const searchTerm = document.getElementById('searchInput').value;
            const roleFilter = document.getElementById('roleFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            
            // 这里可以实现前端筛选或发送AJAX请求到后端
            console.log('应用筛选:', { searchTerm, roleFilter, statusFilter });
            
            // 简单的前端筛选示例
            const rows = document.querySelectorAll('.data-table tbody tr');
            rows.forEach(row => {
                const userName = row.querySelector('.user-name').textContent.toLowerCase();
                const userEmail = row.querySelector('.user-email').textContent.toLowerCase();
                const userRole = row.querySelector('.role-badge').textContent.trim();
                const userStatus = row.querySelector('.status-badge').textContent.trim();
                
                let showRow = true;
                
                // 搜索筛选
                if (searchTerm && !userName.includes(searchTerm.toLowerCase()) && !userEmail.includes(searchTerm.toLowerCase())) {
                    showRow = false;
                }
                
                // 角色筛选
                if (roleFilter && !userRole.includes(roleFilter === 'ADMIN' ? '管理员' : '普通用户')) {
                    showRow = false;
                }
                
                // 状态筛选
                if (statusFilter && !userStatus.includes(statusFilter === 'true' ? '启用' : '禁用')) {
                    showRow = false;
                }
                
                row.style.display = showRow ? '' : 'none';
            });
        }

        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                applyFilters();
            }
        });
    </script>
</body>
</html>
