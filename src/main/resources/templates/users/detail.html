<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>
    <title>用户详情</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .user-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .info-row {
            display: flex;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        .info-row:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
            width: 120px;
            flex-shrink: 0;
        }
        .info-value {
            color: #333;
            flex: 1;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #1976D2;
        }
        .btn-warning {
            background-color: #ff9800;
        }
        .btn-warning:hover {
            background-color: #e68900;
        }
        .btn-danger {
            background-color: #f44336;
        }
        .btn-danger:hover {
            background-color: #da190b;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        .actions {
            margin-top: 30px;
            text-align: center;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #2196F3;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            background-color: #28a745;
            color: white;
            border-radius: 4px;
            font-size: 12px;
        }
        .empty-value {
            color: #6c757d;
            font-style: italic;
        }
        .logout-btn {
            background-color: #f44336;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            float: right;
        }
        .logout-btn:hover {
            background-color: #da190b;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/users" class="back-link">← 返回用户列表</a>
        <form th:action="@{/logout}" method="post" style="float: right; margin: 0;">
            <button type="submit" class="logout-btn" style="border: none; cursor: pointer;">退出登录</button>
        </form>
        <div style="clear: both; margin-bottom: 10px;"></div>
        
        <h1>用户详情</h1>
        
        <div class="user-info">
            <div class="info-row">
                <div class="info-label">用户ID:</div>
                <div class="info-value">
                    <span th:text="${user.id}">1</span>
                    <span class="badge">活跃</span>
                </div>
            </div>
            
            <div class="info-row">
                <div class="info-label">姓名:</div>
                <div class="info-value" th:text="${user.name}">张三</div>
            </div>
            
            <div class="info-row">
                <div class="info-label">邮箱:</div>
                <div class="info-value">
                    <a th:href="'mailto:' + ${user.email}" th:text="${user.email}"><EMAIL></a>
                </div>
            </div>

            <div class="info-row">
                <div class="info-label">手机号:</div>
                <div class="info-value">
                    <span th:if="${user.phone != null and !user.phone.isEmpty()}" th:text="${user.phone}">13800138000</span>
                    <span th:if="${user.phone == null or user.phone.isEmpty()}" class="empty-value">未设置</span>
                </div>
            </div>

            <div class="info-row">
                <div class="info-label">用户名:</div>
                <div class="info-value" th:text="${user.username}">username</div>
            </div>

            <div class="info-row">
                <div class="info-label">角色:</div>
                <div class="info-value">
                    <span th:if="${user.role?.name() == 'ADMIN'}" style="color: #f44336; font-weight: bold;">管理员</span>
                    <span th:if="${user.role?.name() == 'USER'}" style="color: #4CAF50;">普通用户</span>
                </div>
            </div>

            <div class="info-row">
                <div class="info-label">状态:</div>
                <div class="info-value">
                    <span th:if="${user.enabled}" style="color: #4CAF50; font-weight: bold;">启用</span>
                    <span th:if="${!user.enabled}" style="color: #f44336; font-weight: bold;">禁用</span>
                </div>
            </div>
            
            <div class="info-row">
                <div class="info-label">创建时间:</div>
                <div class="info-value" th:text="${#temporals.format(user.createdAt, 'yyyy年MM月dd日 HH:mm:ss')}">
                    2023年01月01日 12:00:00
                </div>
            </div>
            
            <div class="info-row">
                <div class="info-label">更新时间:</div>
                <div class="info-value" th:text="${#temporals.format(user.updatedAt, 'yyyy年MM月dd日 HH:mm:ss')}">
                    2023年01月01日 12:00:00
                </div>
            </div>
        </div>
        
        <div class="actions">
            <a th:href="@{/users/{id}/edit(id=${user.id})}" class="btn btn-warning">编辑用户</a>
            <button class="btn btn-danger" th:onclick="'deleteUser(' + ${user.id} + ')'">删除用户</button>
            <a href="/users" class="btn btn-secondary">返回列表</a>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background-color: #e7f3ff; border-radius: 8px;">
            <h4 style="margin-top: 0; color: #1976D2;">API 信息</h4>
            <p><strong>获取用户API:</strong> <code>GET /users/api/<span th:text="${user.id}">1</span></code></p>
            <p><strong>更新用户API:</strong> <code>PUT /users/api/<span th:text="${user.id}">1</span></code></p>
            <p><strong>删除用户API:</strong> <code>DELETE /users/api/<span th:text="${user.id}">1</span></code></p>
            <a th:href="@{/users/api/{id}(id=${user.id})}" class="btn" target="_blank">查看JSON数据</a>
        </div>
    </div>

    <script>
        function deleteUser(userId) {
            if (confirm('确定要删除这个用户吗？此操作不可撤销。')) {
                // 获取CSRF令牌
                const csrfToken = document.querySelector('meta[name="_csrf"]')?.getAttribute('content');
                const csrfHeader = document.querySelector('meta[name="_csrf_header"]')?.getAttribute('content');

                // 构建请求头
                const headers = {
                    'Content-Type': 'application/json',
                };

                // 添加CSRF令牌到请求头
                if (csrfToken && csrfHeader) {
                    headers[csrfHeader] = csrfToken;
                }

                fetch('/users/api/' + userId, {
                    method: 'DELETE',
                    headers: headers
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('用户删除成功');
                        window.location.href = '/users';
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('删除失败: ' + error.message);
                });
            }
        }
    </script>
</body>
</html>
