<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>
    <title th:text="${user.id != null ? '编辑用户' : '新增用户'}">用户表单</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .form-floating .form-control {
            border-radius: 10px;
        }
        .btn-save {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 10px;
        }
        .btn-save:hover {
            background: linear-gradient(135deg, #218838 0%, #1aa085 100%);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-code-square"></i>
                Java Web应用
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="bi bi-house"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/users">
                            <i class="bi bi-people"></i> 用户管理
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-flask"></i> 测试功能
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/test-merged-user">合并功能测试</a></li>
                            <li><a class="dropdown-item" href="/test-update-user">更新功能测试</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/hello">问候页面</a></li>
                        </ul>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            <span sec:authentication="name">用户</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">用户信息</h6></li>
                            <li><span class="dropdown-item-text">角色: <span sec:authentication="principal.authorities">角色</span></span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form th:action="@{/logout}" method="post" class="d-inline">
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="bi bi-box-arrow-right"></i> 退出登录
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">
                            <i class="bi" th:classappend="${user.id != null ? 'bi-pencil-square' : 'bi-person-plus'}"></i>
                            <span th:text="${user.id != null ? '编辑用户' : '新增用户'}">用户表单</span>
                        </h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="/">首页</a></li>
                                <li class="breadcrumb-item"><a href="/users">用户管理</a></li>
                                <li class="breadcrumb-item active" th:text="${user.id != null ? '编辑用户' : '新增用户'}">用户表单</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="/users" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户表单 -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-person-badge"></i> 用户信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <form th:action="${user.id != null ? '/users/' + user.id : '/users'}" 
                              th:method="${user.id != null ? 'PUT' : 'POST'}" 
                              th:object="${user}" 
                              class="needs-validation" 
                              novalidate>
                            
                            <div class="row g-3">
                                <!-- 姓名 -->
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" 
                                               class="form-control" 
                                               id="name" 
                                               th:field="*{name}" 
                                               placeholder="姓名" 
                                               required>
                                        <label for="name">
                                            <i class="bi bi-person me-2"></i>姓名 *
                                        </label>
                                        <div class="invalid-feedback">
                                            请输入姓名
                                        </div>
                                    </div>
                                </div>

                                <!-- 邮箱 -->
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="email" 
                                               class="form-control" 
                                               id="email" 
                                               th:field="*{email}" 
                                               placeholder="邮箱" 
                                               required>
                                        <label for="email">
                                            <i class="bi bi-envelope me-2"></i>邮箱 *
                                        </label>
                                        <div class="invalid-feedback">
                                            请输入有效的邮箱地址
                                        </div>
                                    </div>
                                </div>

                                <!-- 手机号 -->
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="tel" 
                                               class="form-control" 
                                               id="phone" 
                                               th:field="*{phone}" 
                                               placeholder="手机号">
                                        <label for="phone">
                                            <i class="bi bi-telephone me-2"></i>手机号
                                        </label>
                                    </div>
                                </div>

                                <!-- 用户名 -->
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" 
                                               class="form-control" 
                                               id="username" 
                                               th:field="*{username}" 
                                               placeholder="用户名" 
                                               required>
                                        <label for="username">
                                            <i class="bi bi-person-circle me-2"></i>用户名 *
                                        </label>
                                        <div class="invalid-feedback">
                                            请输入用户名
                                        </div>
                                    </div>
                                </div>

                                <!-- 密码 -->
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="password" 
                                               class="form-control" 
                                               id="password" 
                                               th:field="*{password}" 
                                               placeholder="密码"
                                               th:required="${user.id == null}">
                                        <label for="password">
                                            <i class="bi bi-lock me-2"></i>密码 <span th:if="${user.id == null}">*</span>
                                        </label>
                                        <div class="form-text" th:if="${user.id != null}">
                                            留空则不修改密码
                                        </div>
                                        <div class="invalid-feedback">
                                            请输入密码
                                        </div>
                                    </div>
                                </div>

                                <!-- 角色 -->
                                <div class="col-md-6" sec:authorize="hasRole('ADMIN')">
                                    <div class="form-floating">
                                        <select class="form-select" id="role" th:field="*{role}">
                                            <option value="USER">普通用户</option>
                                            <option value="ADMIN">管理员</option>
                                        </select>
                                        <label for="role">
                                            <i class="bi bi-shield me-2"></i>角色
                                        </label>
                                    </div>
                                </div>

                                <!-- 状态 -->
                                <div class="col-12" sec:authorize="hasRole('ADMIN')">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="enabled" 
                                               th:field="*{enabled}"
                                               th:checked="${user.enabled != null ? user.enabled : true}">
                                        <label class="form-check-label" for="enabled">
                                            <i class="bi bi-toggle-on me-2"></i>启用账户
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="d-flex gap-3">
                                        <button type="submit" class="btn btn-primary btn-save btn-lg">
                                            <i class="bi bi-check-lg me-2"></i>
                                            <span th:text="${user.id != null ? '更新用户' : '创建用户'}">保存</span>
                                        </button>
                                        <a href="/users" class="btn btn-outline-secondary btn-lg">
                                            <i class="bi bi-x-lg me-2"></i>取消
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 帮助信息 -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="bi bi-info-circle me-2"></i>填写说明
                        </h6>
                        <ul class="list-unstyled mb-0">
                            <li><i class="bi bi-check text-success me-2"></i>姓名和邮箱为必填项</li>
                            <li><i class="bi bi-check text-success me-2"></i>邮箱地址必须唯一</li>
                            <li><i class="bi bi-check text-success me-2"></i>用户名用于登录，必须唯一</li>
                            <li th:if="${user.id == null}"><i class="bi bi-check text-success me-2"></i>新用户密码为必填项</li>
                            <li th:if="${user.id != null}"><i class="bi bi-check text-success me-2"></i>编辑时密码留空则不修改</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Bootstrap 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                const forms = document.getElementsByClassName('needs-validation');
                const validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // 自动填充用户名
        document.getElementById('email').addEventListener('blur', function() {
            const usernameField = document.getElementById('username');
            if (!usernameField.value && this.value) {
                usernameField.value = this.value;
            }
        });

        // 密码强度提示
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const feedback = this.nextElementSibling;
            
            if (password.length > 0 && password.length < 6) {
                this.setCustomValidity('密码长度至少6位');
                feedback.textContent = '密码长度至少6位';
            } else {
                this.setCustomValidity('');
                feedback.textContent = '请输入密码';
            }
        });
    </script>
</body>
</html>
