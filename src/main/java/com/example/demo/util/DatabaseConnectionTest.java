package com.example.demo.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * 数据库连接测试工具
 */
public class DatabaseConnectionTest {

    public static void main(String[] args) {
        String url = "********************************************************************************************************************************";
        String username = "root";
        String password = "";

        System.out.println("正在测试数据库连接...");
        System.out.println("URL: " + url);
        System.out.println("用户名: " + username);
        System.out.println("密码: " + (password.isEmpty() ? "(空)" : "***"));

        try {
            // 加载MySQL驱动
            Class.forName("com.mysql.cj.jdbc.Driver");
            System.out.println("MySQL驱动加载成功");

            // 尝试连接数据库
            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("数据库连接成功！");
            
            // 获取数据库信息
            System.out.println("数据库产品名称: " + connection.getMetaData().getDatabaseProductName());
            System.out.println("数据库版本: " + connection.getMetaData().getDatabaseProductVersion());
            System.out.println("驱动版本: " + connection.getMetaData().getDriverVersion());
            
            // 关闭连接
            connection.close();
            System.out.println("连接已关闭");

        } catch (ClassNotFoundException e) {
            System.err.println("MySQL驱动未找到: " + e.getMessage());
        } catch (SQLException e) {
            System.err.println("数据库连接失败: " + e.getMessage());
            System.err.println("错误代码: " + e.getErrorCode());
            System.err.println("SQL状态: " + e.getSQLState());
            
            // 提供一些常见问题的解决建议
            if (e.getMessage().contains("Access denied")) {
                System.err.println("\n可能的解决方案:");
                System.err.println("1. 检查用户名和密码是否正确");
                System.err.println("2. 确认MySQL用户是否允许从当前IP地址连接");
                System.err.println("3. 检查MySQL用户权限设置");
                System.err.println("4. 尝试使用其他用户名（如果有的话）");
            } else if (e.getMessage().contains("Communications link failure")) {
                System.err.println("\n可能的解决方案:");
                System.err.println("1. 检查MySQL服务是否正在运行");
                System.err.println("2. 确认IP地址和端口号是否正确");
                System.err.println("3. 检查防火墙设置");
                System.err.println("4. 确认网络连接是否正常");
            }
        }
    }
}
