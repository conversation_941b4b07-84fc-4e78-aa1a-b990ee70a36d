package com.example.demo.config;

import com.example.demo.entity.User;
import com.example.demo.model.Role;
import com.example.demo.service.UserService;
import com.example.demo.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

/**
 * 数据初始化器
 * 在应用启动时创建默认的管理员用户和角色
 */
@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        // 初始化默认角色
        initializeRoles();
        
        // 初始化默认用户
        initializeUsers();
    }

    private void initializeRoles() {
        try {
            roleService.initializeDefaultRoles();
            System.out.println("默认角色初始化完成");
        } catch (Exception e) {
            System.err.println("角色初始化失败: " + e.getMessage());
        }
    }

    private void initializeUsers() {
        try {
            // 检查是否已存在管理员用户
            if (userService.findByUsername("admin").isEmpty()) {
                // 创建管理员用户
                User admin = new User();
                admin.setName("系统管理员");
                admin.setEmail("<EMAIL>");
                admin.setUsername("admin");
                admin.setPassword(passwordEncoder.encode("admin123"));
                admin.setRole(User.Role.ADMIN);
                admin.setEnabled(true);
                
                // 为管理员分配角色
                Role adminRole = roleService.getRoleByName("ADMIN").orElse(null);
                if (adminRole != null) {
                    admin.addRole(adminRole);
                }
                
                userService.createUser(admin);
                System.out.println("默认管理员用户创建成功: admin/admin123");
            }

            // 检查是否已存在普通用户
            if (userService.findByUsername("user").isEmpty()) {
                // 创建普通用户
                User user = new User();
                user.setName("普通用户");
                user.setEmail("<EMAIL>");
                user.setUsername("user");
                user.setPassword(passwordEncoder.encode("user123"));
                user.setRole(User.Role.USER);
                user.setEnabled(true);
                
                // 为普通用户分配角色
                Role userRole = roleService.getRoleByName("USER").orElse(null);
                if (userRole != null) {
                    user.addRole(userRole);
                }
                
                userService.createUser(user);
                System.out.println("默认普通用户创建成功: user/user123");
            }

            // 创建编辑者用户
            if (userService.findByUsername("editor").isEmpty()) {
                User editor = new User();
                editor.setName("编辑者");
                editor.setEmail("<EMAIL>");
                editor.setUsername("editor");
                editor.setPassword(passwordEncoder.encode("editor123"));
                editor.setRole(User.Role.USER);
                editor.setEnabled(true);
                
                // 为编辑者分配角色
                Role editorRole = roleService.getRoleByName("EDITOR").orElse(null);
                if (editorRole != null) {
                    editor.addRole(editorRole);
                }
                
                userService.createUser(editor);
                System.out.println("默认编辑者用户创建成功: editor/editor123");
            }

        } catch (Exception e) {
            System.err.println("用户初始化失败: " + e.getMessage());
        }
    }
}
