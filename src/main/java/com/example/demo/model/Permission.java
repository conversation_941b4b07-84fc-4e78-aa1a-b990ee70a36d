package com.example.demo.model;

/**
 * 权限枚举
 * 定义系统中的各种权限
 */
public enum Permission {
    // 用户管理权限
    USER_READ("用户查看", "查看用户列表和详情"),
    USER_CREATE("用户创建", "创建新用户"),
    USER_UPDATE("用户编辑", "编辑用户信息"),
    USER_DELETE("用户删除", "删除用户"),
    
    // 角色管理权限
    ROLE_READ("角色查看", "查看角色列表和详情"),
    ROLE_CREATE("角色创建", "创建新角色"),
    ROLE_UPDATE("角色编辑", "编辑角色信息"),
    ROLE_DELETE("角色删除", "删除角色"),
    
    // 系统管理权限
    SYSTEM_CONFIG("系统配置", "系统配置管理"),
    SYSTEM_LOG("系统日志", "查看系统日志"),
    
    // 数据权限
    DATA_EXPORT("数据导出", "导出系统数据"),
    DATA_IMPORT("数据导入", "导入系统数据");

    private final String displayName;
    private final String description;

    Permission(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return this.name();
    }
}
