package com.example.demo.repository;

import com.example.demo.entity.AppUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 应用用户数据访问层接口
 */
@Repository
public interface AppUserRepository extends JpaRepository<AppUser, Long> {

    /**
     * 根据用户名查找用户
     */
    Optional<AppUser> findByUsername(String username);

    /**
     * 根据邮箱查找用户
     */
    Optional<AppUser> findByEmail(String email);

    /**
     * 检查用户名是否已存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否已存在
     */
    boolean existsByEmail(String email);
}
