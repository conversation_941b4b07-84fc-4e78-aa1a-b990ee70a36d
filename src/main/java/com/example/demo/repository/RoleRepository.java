package com.example.demo.repository;

import com.example.demo.model.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 角色数据访问层
 */
@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    
    /**
     * 根据角色名称查找角色
     */
    Optional<Role> findByName(String name);
    
    /**
     * 检查角色名称是否存在
     */
    boolean existsByName(String name);
    
    /**
     * 查找所有启用的角色
     */
    List<Role> findByEnabledTrue();
    
    /**
     * 查找所有禁用的角色
     */
    List<Role> findByEnabledFalse();
    
    /**
     * 根据显示名称查找角色
     */
    Optional<Role> findByDisplayName(String displayName);
    
    /**
     * 根据角色名称模糊查询
     */
    @Query("SELECT r FROM Role r WHERE r.name LIKE %:name% OR r.displayName LIKE %:name%")
    List<Role> findByNameContaining(@Param("name") String name);
    
    /**
     * 查找用户拥有的角色
     */
    @Query("SELECT r FROM Role r JOIN r.users u WHERE u.id = :userId")
    List<Role> findByUserId(@Param("userId") Long userId);
    
    /**
     * 统计角色数量
     */
    @Query("SELECT COUNT(r) FROM Role r WHERE r.enabled = true")
    long countEnabledRoles();
}
