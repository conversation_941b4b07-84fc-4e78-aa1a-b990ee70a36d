package com.example.demo;

import com.example.demo.service.AppUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 主应用程序类
 * 这是Spring Boot应用程序的入口点
 */
@SpringBootApplication
public class Application implements CommandLineRunner {

    @Autowired
    private AppUserService appUserService;

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        // 创建默认管理员用户
        appUserService.createDefaultAdminIfNotExists();
        System.out.println("默认管理员用户检查完成");
    }
}
