package com.example.demo.service;

import com.example.demo.entity.AppUser;
import com.example.demo.repository.AppUserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 应用用户服务类
 */
@Service
@Transactional
public class AppUserService implements UserDetailsService {

    @Autowired
    private AppUserRepository appUserRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * Spring Security用户详情加载
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        return appUserRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("用户不存在: " + username));
    }

    /**
     * 获取所有用户
     */
    public List<AppUser> getAllUsers() {
        return appUserRepository.findAll();
    }

    /**
     * 根据ID获取用户
     */
    public Optional<AppUser> getUserById(Long id) {
        return appUserRepository.findById(id);
    }

    /**
     * 根据用户名获取用户
     */
    public Optional<AppUser> getUserByUsername(String username) {
        return appUserRepository.findByUsername(username);
    }

    /**
     * 创建新用户
     */
    public AppUser createUser(AppUser user) {
        // 检查用户名是否已存在
        if (appUserRepository.existsByUsername(user.getUsername())) {
            throw new RuntimeException("用户名已存在: " + user.getUsername());
        }
        
        // 检查邮箱是否已存在
        if (appUserRepository.existsByEmail(user.getEmail())) {
            throw new RuntimeException("邮箱已存在: " + user.getEmail());
        }
        
        // 加密密码
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        return appUserRepository.save(user);
    }

    /**
     * 更新用户信息
     */
    public AppUser updateUser(Long id, AppUser userDetails) {
        AppUser user = appUserRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在，ID: " + id));

        // 检查用户名是否被其他用户使用
        if (!user.getUsername().equals(userDetails.getUsername()) 
            && appUserRepository.existsByUsername(userDetails.getUsername())) {
            throw new RuntimeException("用户名已被其他用户使用: " + userDetails.getUsername());
        }

        // 检查邮箱是否被其他用户使用
        if (!user.getEmail().equals(userDetails.getEmail())
            && appUserRepository.existsByEmail(userDetails.getEmail())) {
            throw new RuntimeException("邮箱已被其他用户使用: " + userDetails.getEmail());
        }

        user.setUsername(userDetails.getUsername());
        user.setEmail(userDetails.getEmail());
        user.setDisplayName(userDetails.getDisplayName());
        user.setRole(userDetails.getRole());
        user.setEnabled(userDetails.isEnabled());

        // 如果提供了新密码，则更新密码
        if (userDetails.getPassword() != null && !userDetails.getPassword().isEmpty()) {
            user.setPassword(passwordEncoder.encode(userDetails.getPassword()));
        }

        return appUserRepository.save(user);
    }

    /**
     * 删除用户
     */
    public void deleteUser(Long id) {
        AppUser user = appUserRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在，ID: " + id));
        appUserRepository.delete(user);
    }

    /**
     * 获取用户总数
     */
    public long getUserCount() {
        return appUserRepository.count();
    }

    /**
     * 检查用户名是否已存在
     */
    public boolean isUsernameExists(String username) {
        return appUserRepository.existsByUsername(username);
    }

    /**
     * 检查邮箱是否已存在
     */
    public boolean isEmailExists(String email) {
        return appUserRepository.existsByEmail(email);
    }

    /**
     * 创建默认管理员用户（如果不存在）
     */
    public void createDefaultAdminIfNotExists() {
        if (!appUserRepository.existsByUsername("admin")) {
            AppUser admin = new AppUser();
            admin.setUsername("admin");
            admin.setPassword("admin123"); // 将被加密
            admin.setEmail("<EMAIL>");
            admin.setDisplayName("系统管理员");
            admin.setRole(AppUser.Role.ADMIN);
            admin.setEnabled(true);
            
            createUser(admin);
        }
    }
}
