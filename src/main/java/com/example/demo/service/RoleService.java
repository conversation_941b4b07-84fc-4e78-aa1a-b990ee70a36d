package com.example.demo.service;

import com.example.demo.model.Permission;
import com.example.demo.model.Role;
import com.example.demo.repository.RoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 角色服务类
 */
@Service
@Transactional
public class RoleService {

    @Autowired
    private RoleRepository roleRepository;

    /**
     * 获取所有角色
     */
    public List<Role> getAllRoles() {
        return roleRepository.findAll();
    }

    /**
     * 获取所有启用的角色
     */
    public List<Role> getEnabledRoles() {
        return roleRepository.findByEnabledTrue();
    }

    /**
     * 根据ID获取角色
     */
    public Optional<Role> getRoleById(Long id) {
        return roleRepository.findById(id);
    }

    /**
     * 根据名称获取角色
     */
    public Optional<Role> getRoleByName(String name) {
        return roleRepository.findByName(name);
    }

    /**
     * 创建角色
     */
    public Role createRole(Role role) {
        // 检查角色名称是否已存在
        if (roleRepository.existsByName(role.getName())) {
            throw new RuntimeException("角色名称已存在: " + role.getName());
        }
        return roleRepository.save(role);
    }

    /**
     * 更新角色
     */
    public Role updateRole(Long id, Role roleDetails) {
        Optional<Role> optionalRole = roleRepository.findById(id);
        if (optionalRole.isPresent()) {
            Role role = optionalRole.get();
            
            // 检查角色名称是否与其他角色冲突
            if (!role.getName().equals(roleDetails.getName()) && 
                roleRepository.existsByName(roleDetails.getName())) {
                throw new RuntimeException("角色名称已存在: " + roleDetails.getName());
            }
            
            role.setName(roleDetails.getName());
            role.setDisplayName(roleDetails.getDisplayName());
            role.setDescription(roleDetails.getDescription());
            role.setPermissions(roleDetails.getPermissions());
            role.setEnabled(roleDetails.getEnabled());
            
            return roleRepository.save(role);
        } else {
            throw new RuntimeException("角色不存在，ID: " + id);
        }
    }

    /**
     * 删除角色
     */
    public void deleteRole(Long id) {
        if (roleRepository.existsById(id)) {
            roleRepository.deleteById(id);
        } else {
            throw new RuntimeException("角色不存在，ID: " + id);
        }
    }

    /**
     * 为角色添加权限
     */
    public Role addPermissionToRole(Long roleId, Permission permission) {
        Optional<Role> optionalRole = roleRepository.findById(roleId);
        if (optionalRole.isPresent()) {
            Role role = optionalRole.get();
            role.addPermission(permission);
            return roleRepository.save(role);
        } else {
            throw new RuntimeException("角色不存在，ID: " + roleId);
        }
    }

    /**
     * 从角色移除权限
     */
    public Role removePermissionFromRole(Long roleId, Permission permission) {
        Optional<Role> optionalRole = roleRepository.findById(roleId);
        if (optionalRole.isPresent()) {
            Role role = optionalRole.get();
            role.removePermission(permission);
            return roleRepository.save(role);
        } else {
            throw new RuntimeException("角色不存在，ID: " + roleId);
        }
    }

    /**
     * 设置角色权限
     */
    public Role setRolePermissions(Long roleId, Set<Permission> permissions) {
        Optional<Role> optionalRole = roleRepository.findById(roleId);
        if (optionalRole.isPresent()) {
            Role role = optionalRole.get();
            role.setPermissions(permissions);
            return roleRepository.save(role);
        } else {
            throw new RuntimeException("角色不存在，ID: " + roleId);
        }
    }

    /**
     * 启用/禁用角色
     */
    public Role toggleRoleStatus(Long id) {
        Optional<Role> optionalRole = roleRepository.findById(id);
        if (optionalRole.isPresent()) {
            Role role = optionalRole.get();
            role.setEnabled(!role.getEnabled());
            return roleRepository.save(role);
        } else {
            throw new RuntimeException("角色不存在，ID: " + id);
        }
    }

    /**
     * 搜索角色
     */
    public List<Role> searchRoles(String keyword) {
        return roleRepository.findByNameContaining(keyword);
    }

    /**
     * 获取角色数量
     */
    public long getRoleCount() {
        return roleRepository.count();
    }

    /**
     * 获取启用角色数量
     */
    public long getEnabledRoleCount() {
        return roleRepository.countEnabledRoles();
    }

    /**
     * 初始化默认角色
     */
    @Transactional
    public void initializeDefaultRoles() {
        // 创建管理员角色
        if (!roleRepository.existsByName("ADMIN")) {
            Role adminRole = new Role("ADMIN", "系统管理员", "拥有系统所有权限的管理员角色");
            // 添加所有权限
            for (Permission permission : Permission.values()) {
                adminRole.addPermission(permission);
            }
            roleRepository.save(adminRole);
        }

        // 创建普通用户角色
        if (!roleRepository.existsByName("USER")) {
            Role userRole = new Role("USER", "普通用户", "系统普通用户角色");
            // 添加基本权限
            userRole.addPermission(Permission.USER_READ);
            roleRepository.save(userRole);
        }

        // 创建编辑者角色
        if (!roleRepository.existsByName("EDITOR")) {
            Role editorRole = new Role("EDITOR", "编辑者", "可以编辑用户信息的角色");
            editorRole.addPermission(Permission.USER_READ);
            editorRole.addPermission(Permission.USER_UPDATE);
            roleRepository.save(editorRole);
        }
    }
}
