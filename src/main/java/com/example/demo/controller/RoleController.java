package com.example.demo.controller;

import com.example.demo.model.Permission;
import com.example.demo.model.Role;
import com.example.demo.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 角色管理控制器
 */
@Controller
@RequestMapping("/roles")
public class RoleController {

    @Autowired
    private RoleService roleService;

    /**
     * 显示角色列表页面
     */
    @GetMapping
    @PreAuthorize("hasAuthority('ROLE_READ') or hasRole('ADMIN')")
    public String listRoles(Model model) {
        List<Role> roles = roleService.getAllRoles();
        model.addAttribute("roles", roles);
        model.addAttribute("roleCount", roleService.getRoleCount());
        return "roles/list-bootstrap";
    }

    /**
     * 显示角色详情页面
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('ROLE_READ') or hasRole('ADMIN')")
    public String showRole(@PathVariable Long id, Model model) {
        Optional<Role> role = roleService.getRoleById(id);
        if (role.isPresent()) {
            model.addAttribute("role", role.get());
            model.addAttribute("allPermissions", Permission.values());
            return "roles/detail-bootstrap";
        } else {
            return "redirect:/roles?error=notfound";
        }
    }

    /**
     * 显示创建角色表单页面
     */
    @GetMapping("/new")
    @PreAuthorize("hasAuthority('ROLE_CREATE') or hasRole('ADMIN')")
    public String showCreateForm(Model model) {
        model.addAttribute("role", new Role());
        model.addAttribute("allPermissions", Permission.values());
        model.addAttribute("isEdit", false);
        return "roles/form-bootstrap";
    }

    /**
     * 显示编辑角色表单页面
     */
    @GetMapping("/{id}/edit")
    @PreAuthorize("hasAuthority('ROLE_UPDATE') or hasRole('ADMIN')")
    public String showEditForm(@PathVariable Long id, Model model) {
        Optional<Role> role = roleService.getRoleById(id);
        if (role.isPresent()) {
            model.addAttribute("role", role.get());
            model.addAttribute("allPermissions", Permission.values());
            model.addAttribute("isEdit", true);
            return "roles/form-bootstrap";
        } else {
            return "redirect:/roles?error=notfound";
        }
    }

    /**
     * 创建角色
     */
    @PostMapping
    @PreAuthorize("hasAuthority('ROLE_CREATE') or hasRole('ADMIN')")
    public String createRole(@ModelAttribute Role role) {
        try {
            roleService.createRole(role);
            return "redirect:/roles?success=created";
        } catch (Exception e) {
            return "redirect:/roles/new?error=" + e.getMessage();
        }
    }

    /**
     * 更新角色
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('ROLE_UPDATE') or hasRole('ADMIN')")
    public String updateRole(@PathVariable Long id, @ModelAttribute Role role) {
        try {
            roleService.updateRole(id, role);
            return "redirect:/roles/" + id + "?success=updated";
        } catch (Exception e) {
            return "redirect:/roles/" + id + "/edit?error=" + e.getMessage();
        }
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('ROLE_DELETE') or hasRole('ADMIN')")
    public String deleteRole(@PathVariable Long id) {
        try {
            roleService.deleteRole(id);
            return "redirect:/roles?success=deleted";
        } catch (Exception e) {
            return "redirect:/roles?error=" + e.getMessage();
        }
    }

    /**
     * 切换角色状态
     */
    @PostMapping("/{id}/toggle")
    @PreAuthorize("hasAuthority('ROLE_UPDATE') or hasRole('ADMIN')")
    public String toggleRoleStatus(@PathVariable Long id) {
        try {
            roleService.toggleRoleStatus(id);
            return "redirect:/roles?success=toggled";
        } catch (Exception e) {
            return "redirect:/roles?error=" + e.getMessage();
        }
    }

    // REST API 端点

    /**
     * 获取所有角色 API
     */
    @GetMapping("/api")
    @PreAuthorize("hasAuthority('ROLE_READ') or hasRole('ADMIN')")
    @ResponseBody
    public ResponseEntity<List<Role>> getAllRolesApi() {
        List<Role> roles = roleService.getAllRoles();
        return ResponseEntity.ok(roles);
    }

    /**
     * 获取角色详情 API
     */
    @GetMapping("/api/{id}")
    @PreAuthorize("hasAuthority('ROLE_READ') or hasRole('ADMIN')")
    @ResponseBody
    public ResponseEntity<Role> getRoleApi(@PathVariable Long id) {
        Optional<Role> role = roleService.getRoleById(id);
        return role.map(ResponseEntity::ok)
                  .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 创建角色 API
     */
    @PostMapping("/api")
    @PreAuthorize("hasAuthority('ROLE_CREATE') or hasRole('ADMIN')")
    @ResponseBody
    public ResponseEntity<Role> createRoleApi(@RequestBody Role role) {
        try {
            Role createdRole = roleService.createRole(role);
            return ResponseEntity.ok(createdRole);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 更新角色 API
     */
    @PutMapping("/api/{id}")
    @PreAuthorize("hasAuthority('ROLE_UPDATE') or hasRole('ADMIN')")
    @ResponseBody
    public ResponseEntity<Role> updateRoleApi(@PathVariable Long id, @RequestBody Role role) {
        try {
            Role updatedRole = roleService.updateRole(id, role);
            return ResponseEntity.ok(updatedRole);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 删除角色 API
     */
    @DeleteMapping("/api/{id}")
    @PreAuthorize("hasAuthority('ROLE_DELETE') or hasRole('ADMIN')")
    @ResponseBody
    public ResponseEntity<Void> deleteRoleApi(@PathVariable Long id) {
        try {
            roleService.deleteRole(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 搜索角色 API
     */
    @GetMapping("/api/search")
    @PreAuthorize("hasAuthority('ROLE_READ') or hasRole('ADMIN')")
    @ResponseBody
    public ResponseEntity<List<Role>> searchRolesApi(@RequestParam String keyword) {
        List<Role> roles = roleService.searchRoles(keyword);
        return ResponseEntity.ok(roles);
    }

    /**
     * 获取所有权限 API
     */
    @GetMapping("/api/permissions")
    @PreAuthorize("hasAuthority('ROLE_READ') or hasRole('ADMIN')")
    @ResponseBody
    public ResponseEntity<Permission[]> getAllPermissionsApi() {
        return ResponseEntity.ok(Permission.values());
    }
}
