package com.example.demo.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 认证控制器
 */
@Controller
public class AuthController {

    /**
     * 显示登录页面
     */
    @GetMapping("/login")
    public String login(
            @RequestParam(value = "error", required = false) String error,
            @RequestParam(value = "logout", required = false) String logout,
            Model model) {
        
        if (error != null) {
            model.addAttribute("error", "用户名或密码错误，请重试。");
        }
        
        if (logout != null) {
            model.addAttribute("message", "您已成功退出登录。");
        }
        
        return "auth/login";
    }

    /**
     * 登录成功页面
     */
    @GetMapping("/login-success")
    public String loginSuccess() {
        return "auth/login-success";
    }

    /**
     * 测试登出功能页面
     */
    @GetMapping("/test-logout")
    public String testLogout() {
        return "test-logout";
    }

    /**
     * 测试合并用户功能页面
     */
    @GetMapping("/test-merged-user")
    public String testMergedUser() {
        return "test-merged-user";
    }
}
