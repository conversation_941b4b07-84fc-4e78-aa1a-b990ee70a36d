package com.example.demo.controller;

import com.example.demo.entity.User;
import com.example.demo.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 测试控制器
 */
@Controller
@RequestMapping("/test")
public class TestController {

    @Autowired
    private UserService userService;

    /**
     * 创建测试用户
     */
    @GetMapping("/create-user")
    @ResponseBody
    public String createTestUser() {
        try {
            User user = new User("测试用户", "<EMAIL>", "13800138000");
            User savedUser = userService.createUser(user);
            return "测试用户创建成功！ID: " + savedUser.getId();
        } catch (Exception e) {
            return "创建失败: " + e.getMessage();
        }
    }

    /**
     * 获取用户数量
     */
    @GetMapping("/count")
    @ResponseBody
    public String getUserCount() {
        try {
            long count = userService.getUserCount();
            return "当前用户数量: " + count;
        } catch (Exception e) {
            return "获取用户数量失败: " + e.getMessage();
        }
    }
}
