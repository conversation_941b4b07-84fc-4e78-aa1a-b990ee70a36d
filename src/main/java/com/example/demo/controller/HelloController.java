package com.example.demo.controller;

import com.example.demo.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Hello控制器
 * 处理基本的HTTP请求
 */
@Controller
public class HelloController {

    @Autowired
    private UserService userService;

    /**
     * 返回JSON响应的API端点
     */
    @GetMapping("/api/hello")
    @ResponseBody
    public String helloApi(@RequestParam(value = "name", defaultValue = "World") String name) {
        return String.format("{\"message\": \"Hello, %s!\"}", name);
    }

    /**
     * 返回现代化首页
     */
    @GetMapping("/")
    public String index(Model model) {
        model.addAttribute("title", "欢迎使用Java Web应用");
        model.addAttribute("message", "这是一个基于Spring Boot的演示应用，已连接MySQL数据库");

        // 添加数据库状态信息
        try {
            long userCount = userService.getUserCount();
            model.addAttribute("dbStatus", "数据库连接正常");
            model.addAttribute("userCount", userCount);
        } catch (Exception e) {
            model.addAttribute("dbStatus", "数据库连接失败: " + e.getMessage());
            model.addAttribute("userCount", 0);
        }

        return "index-modern";
    }

    /**
     * 返回传统首页
     */
    @GetMapping("/classic")
    public String indexClassic(Model model) {
        model.addAttribute("title", "欢迎使用Java Web应用");
        model.addAttribute("message", "这是一个基于Spring Boot的演示应用，已连接MySQL数据库");

        // 添加数据库状态信息
        try {
            long userCount = userService.getUserCount();
            model.addAttribute("dbStatus", "数据库连接正常");
            model.addAttribute("userCount", userCount);
        } catch (Exception e) {
            model.addAttribute("dbStatus", "数据库连接失败: " + e.getMessage());
            model.addAttribute("userCount", 0);
        }

        return "index";
    }

    /**
     * 带参数的页面端点
     */
    @GetMapping("/hello")
    public String hello(@RequestParam(value = "name", defaultValue = "访客") String name, Model model) {
        model.addAttribute("name", name);
        return "hello";
    }
}
