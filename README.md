# Java Web 演示项目

这是一个基于Spring Boot的Java Web应用程序演示项目。

## 项目特性

- **Spring Boot 3.2.0** - 现代化的Java Web框架
- **Maven** - 项目构建和依赖管理
- **Thymeleaf** - 服务端模板引擎
- **Spring Web MVC** - Web层框架
- **JUnit 5** - 单元测试框架
- **Spring Boot DevTools** - 开发工具，支持热重载

## 项目结构

```
demo-web-app/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/example/demo/
│   │   │       ├── Application.java              # 主应用程序类
│   │   │       └── controller/
│   │   │           └── HelloController.java      # Web控制器
│   │   └── resources/
│   │       ├── static/
│   │       │   └── css/
│   │       │       └── style.css                 # CSS样式文件
│   │       ├── templates/
│   │       │   ├── index.html                    # 首页模板
│   │       │   └── hello.html                    # 问候页面模板
│   │       └── application.properties            # 应用配置
│   └── test/
│       └── java/
│           └── com/example/demo/
│               ├── ApplicationTests.java         # 应用程序测试
│               └── controller/
│                   └── HelloControllerTest.java  # 控制器测试
├── pom.xml                                       # Maven配置文件
└── README.md                                     # 项目说明文档
```

## 快速开始

### 前提条件

- Java 17 或更高版本
- Maven 3.6 或更高版本

### 运行应用程序

1. 克隆或下载项目到本地
2. 在项目根目录执行以下命令：

```bash
# 编译项目
mvn clean compile

# 运行应用程序
mvn spring-boot:run
```

3. 打开浏览器访问 `http://localhost:8080`

### 运行测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=HelloControllerTest
```

### 打包应用程序

```bash
# 创建可执行JAR文件
mvn clean package

# 运行打包后的JAR文件
java -jar target/demo-web-app-1.0.0.jar
```

## API端点

### Web页面

- `GET /` - 首页
- `GET /hello` - 问候页面
- `GET /hello?name=姓名` - 带参数的问候页面

### REST API

- `GET /api/hello` - 返回JSON格式的问候消息
- `GET /api/hello?name=姓名` - 返回带参数的JSON问候消息

## 开发说明

### 热重载

项目已配置Spring Boot DevTools，在开发过程中修改代码后会自动重启应用程序。

### 添加新功能

1. 在 `src/main/java/com/example/demo/controller/` 目录下创建新的控制器
2. 在 `src/main/resources/templates/` 目录下添加对应的HTML模板
3. 在 `src/test/java/` 目录下编写相应的测试

### 配置修改

应用程序的配置可以在 `src/main/resources/application.properties` 文件中修改。

## 技术栈

- **后端**: Spring Boot, Spring Web MVC
- **模板引擎**: Thymeleaf
- **构建工具**: Maven
- **测试**: JUnit 5, Spring Boot Test
- **前端**: HTML, CSS (可扩展为React/Vue等)

## 许可证

本项目仅用于学习和演示目的。
